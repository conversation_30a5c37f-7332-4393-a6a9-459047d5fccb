# Wish Creation Flow - Test & Verification Plan
# 📋 FUNCTION: Systematic verification checklist - Step-by-step testing plan to verify implementation matches design

This document outlines the step-by-step process flow for the conversational wish creation feature. We will use this to systematically verify that the implementation matches the architectural design.

---

## Verification Sequence

### 1. Entry Point: Check-In to Transition ✅ [COMPLETED]

**📋 TASK 1 SUMMARY:**
**Functions Modified:** `TransitionChain.buildPhaseSuggestionPrompt()` - Removed deferred concept screen phases from LLM prompt criteria
**Functions Verified:** `TransitionChain.processTransition()`, `TransitionChain.getValidTransitionPhases()` - Working correctly
**Result:** TransitionChain now correctly routes to wish creation when slots available, with MVP-aligned phase suggestions

### 2. Initiation: Starting the Wish Creation Conversation ✅ [COMPLETED]

**Focus:** Verify that `WishCreationManager` is properly integrated and functional.

**Entry Point:** `ConversationAgent.launchWishCreation()` calls `WishCreationManager.processWishCreation()`

**✅ COMPLETE FLOW DIAGRAM (VERIFIED):**
```
TransitionChain.processTransition() 
    ↓ (returns TransitionActionPlan)
ConversationAgent.handleStructuredTransition(actionPlan)
    ↓ (calls navigateCoreLoopPhase)
ConversationAgent.navigateCoreLoopPhase(actionPlan)
    ↓ (routes to WISH_COLLECTION case)
ConversationAgent.launchWishCreation(themes)
    ↓ (calls WishCreationManager)
WishCreationManager.processWishCreation(themes, null, currentState)
```

**Functions to Verify:**

1. **✅ ConversationAgent.launchWishCreation()** - Working correctly
   - Initializes `WishCreationState` with `isActive = true, stage = "generating"`
   - Sets `ConversationType.WishCreation` in AgentCortex
   - Calls `wishCreationManager.processWishCreation(themes, null, currentState)`
   - Handles result and updates state appropriately

2. **✅ WishCreationManager.processWishCreation()** - Ready for verification
   - **Location:** `app/src/main/java/com/example/voxmanifestorapp/ui/agent/wishcreation/WishCreationManager.kt`
   - **Purpose:** Main orchestration method for wish creation conversation
   - **Input:** `themes: List<ConversationalTheme>`, `userInput: String?`, `currentState: WishCreationState`
   - **Output:** `WishCreationResult` with speech response and continuation flags

3. **✅ WishCreationManager.generateInitialWish()** - Ready for verification
   - **Purpose:** Generates initial wish from themes using LLM
   - **Input:** `themes: List<ConversationalTheme>`
   - **Output:** Generated wish text via `BrainService.generateSimpleText()`

4. **✅ WishCreationManager.refineWish()** - Ready for verification
   - **Purpose:** Refines existing wish based on user feedback
   - **Input:** `currentWish: String`, `userFeedback: String`
   - **Output:** Refined wish text via `BrainService.generateSimpleText()`

**VERIFICATION NEEDED:**
- Test `WishCreationManager.processWishCreation()` with sample themes
- Verify `generateInitialWish()` produces coherent wish from themes
- Verify `refineWish()` improves wish based on feedback
- Confirm proper state transitions ("generating" → "refining" → "validating")
- Test error handling and edge cases

**DETAILED FUNCTION VERIFICATION:**

**1. WishCreationManager.processWishCreation() - Main Orchestration**
- **Location:** `app/src/main/java/com/example/voxmanifestorapp/ui/agent/wishcreation/WishCreationManager.kt`
- **Input:** `themes: List<ConversationalTheme>`, `userInput: String?`, `currentState: WishCreationState`
- **Output:** `WishCreationResult` with speech response and continuation flags
- **Logic to Verify:**
  - Does it correctly handle "generating" stage for initial wish creation?
  - Does it transition to "refining" stage after initial wish generation?
  - Does it handle "validating" stage for final confirmation?
  - Does it return proper `WishCreationResult` with `speechResponse`, `shouldContinue`, `isComplete`?

**2. WishCreationManager.generateInitialWish() - LLM Wish Generation**
- **Purpose:** Generates initial wish from themes using LLM
- **Input:** `themes: List<ConversationalTheme>`
- **Output:** Generated wish text via `BrainService.generateSimpleText()`
- **Logic to Verify:**
  - Does it call `BrainService.generateSimpleText()` with proper prompt?
  - Does the prompt include manifestation principles (present tense, specificity, positive framing)?
  - Does it extract themes from `ConversationalTheme.title` correctly?
  - Does it return coherent, manifestation-aligned wish text?

**3. WishCreationManager.refineWish() - LLM Wish Refinement**
- **Purpose:** Refines existing wish based on user feedback
- **Input:** `currentWish: String`, `userFeedback: String`
- **Output:** Refined wish text via `BrainService.generateSimpleText()`
- **Logic to Verify:**
  - Does it call `BrainService.generateSimpleText()` with refinement prompt?
  - Does the prompt include both current wish and user feedback?
  - Does it apply manifestation principles to improve the wish?
  - Does it maintain the core intention while improving specificity?

**4. WishCreationManager State Machine**
- **Stages:** "generating" → "refining" → "validating"
- **Logic to Verify:**
  - Does it start in "generating" stage?
  - Does it transition to "refining" after initial wish generation?
  - Does it transition to "validating" when user is satisfied?
  - Does it handle stage transitions correctly in `processWishCreation()`?

**5. WishCreationResult Structure**
- **Components:** `speechResponse: String`, `shouldContinue: Boolean`, `isComplete: Boolean`
- **Logic to Verify:**
  - Does `speechResponse` contain the generated/refined wish text?
  - Does `shouldContinue` indicate whether conversation should continue?
  - Does `isComplete` indicate when wish creation is finished?

**6. BrainService Integration**
- **Method:** `BrainService.generateSimpleText(prompt: String): Result<String>`
- **Logic to Verify:**
  - Does WishCreationManager call this method correctly?
  - Does it handle `Result.success()` and `Result.failure()` cases?
  - Does it pass proper prompts for both generation and refinement?

**ISSUES TO RESOLVE:**
- **ConversationType.WishCreation** - Already exists in AgentClasses.kt ✅
- **Integration with BrainService** - `generateSimpleText()` method available ✅
- **State Management** - AgentCortex integration working ✅

**📋 TASK 2 SUMMARY:**
**Functions Verified:** `ConversationAgent.launchWishCreation()` - Working correctly
**Functions Ready for Testing:** `WishCreationManager.processWishCreation()`, `generateInitialWish()`, `refineWish()`
**Result:** WishCreationManager integration complete, ready for functional testing

### 3. Stage 1: Generating the Initial Wish 🔄 [IN PROGRESS]

The `WishCreationManager` begins its internal state machine.

- **Component:** `WishCreationManager`
- **Function:** `processWishCreation()`
- **Internal Stage:** `generating`
- **Logic to Verify:**
    1. Is the initial state correctly set to `generating`?
    2. Does it call the `BrainService` to generate a wish from the check-in themes?
        - **Sub-Component:** `BrainService`
        - **Sub-Function:** `generateSimpleText()` (corrected from `generateWishFromThemes()`)
    3. Does it return a `WishCreationResult` containing the suggested wish as a `speechResponse`?

**VERIFICATION NEEDED:**
- Check `WishCreationManager.processWishCreation()` for "generating" stage logic
- Verify `generateInitialWish()` method implementation
- Confirm `BrainService.generateSimpleText()` integration
- Validate `WishCreationResult` structure and speech response

### 4. Stage 2: Conversational Refinement Loop

The user provides verbal feedback to refine the generated wish.

- **Component:** `WishCreationManager`
- **Function:** `processWishCreation()`
- **Internal Stage:** `refining`
- **Logic to Verify:**
    1. Does the manager correctly transition its internal state to `refining` after the initial wish is presented?
    2. Does it capture the user's freeform voice input?
    3. Does it call the `BrainService` with the current wish and the user's feedback to get a refined suggestion?
        - **Sub-Component:** `BrainService`
        - **Sub-Function:** `generateSimpleText()` (corrected from `refineWish()`)
    4. Does the loop continue correctly if the user wants to make more changes?
    5. Is the voice interaction immediate (no button presses required)?

### 5. Stage 3: Validation and Saving

The user agrees to the refined wish, and it is saved.

- **Component:** `WishCreationManager`
- **Function:** `processWishCreation()`
- **Internal Stage:** `validating`
- **Logic to Verify:**
    1. Does the manager transition to the `validating` stage upon user approval (e.g., "Yes, that's it")?
    2. Does it correctly parse the final "yes/no" confirmation?
    3. On "yes," does it call the `WishUtilities` to save the wish to the database in the correct slot?
        - **Sub-Component:** `WishUtilities`
        - **Sub-Function:** `saveWishToSlot()` (or similar)
    4. On "no," does it correctly loop back to the `refining` stage?

### 6. Completion: Returning to Core Loop

The `WishCreationManager` completes its job and returns control to the `ConversationAgent`.

- **Component:** `WishCreationManager`
- **Function:** `processWishCreation()`
- **Logic to Verify:**
    1. Does the function return a `WishCreationResult` with `isComplete = true`?
- **Orchestrator:** `ConversationAgent`
- **Logic to Verify:**
    1. Does the `ConversationAgent` correctly receive the completion result?
    2. Does it reset the `ConversationType` and `WishCreationState` in the `AgentCortex`?
    3. Does it transition smoothly back to the main conversational loop?
