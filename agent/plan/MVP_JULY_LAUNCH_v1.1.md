# MVP July Launch v1.1 - Product Requirements Document

## Background and Motivation

Following a strategic review to meet a two-week deadline, we are shifting to a **hyper-focused MVP approach**. This revised plan prioritizes the most critical, high-value features that deliver a complete and demonstrable user journey. The core strengths of the check-in system, theme extraction, and conversational interaction remain the foundation.

**Strategic Focus**: Create a workable, polished, and downloadable app that one close friend can start using, establishing the foundation for user feedback and iterative development.

## High-level Task Breakdown

### Phase 1: Core Conversational Experience
**Priority: Critical**

1.  **Theme Visualization System**
    -   Create a dedicated theme display screen or pop-up.
    -   Show extracted themes (`ConversationalTheme` data) in an organized list format.
    -   Include the ability to view themes from previous sessions.
    -   **Success Criteria**: User can easily review and understand conversation themes within the app.

2.  **Conversation Replay & Summarization**
    -   Add a "repeat last response" functionality.
    -   Create a "summarize themes" voice command for the agent.
    -   Enable easy user access to the agent's analysis and insights.
    -   **Success Criteria**: User can request and receive a spoken summary of their extracted conversational themes.

### Phase 2: Conversational Goal Management
**Priority: Critical**

3.  **Conversational Goal Creation & Editing**
    -   Implement an LLM-driven goal definition process.
    -   Enable the creation, editing, and removal of the 5 main life goals (wishes) entirely through dialogue.
    -   Replace any basic CRUD operations with a guided, natural conversation.
    -   **Success Criteria**: User can fully manage their 5 core wishes entirely through conversation.

## Deferred to Post-MVP

The following features from the original v1.0 plan have been intentionally deferred to a post-MVP release to ensure a focused and timely launch.

---

0.  **ConversationAgent Architectural Completion**
    -   **Task**: Complete the final modularization of `ConversationAgent` by extracting all concept-building logic into a new, dedicated `ConceptManager`.
    -   **Technical Benefits**: Reduces the size and complexity of `ConversationAgent`, completing the 6-module refactoring effort and improving long-term maintainability.
    -   **Reason for Deferral**: While architecturally beneficial, this is a non-user-facing refactor. The existing integration is functional for the MVP scope. Deferring this allows us to focus all effort on delivering user-facing features.
    -   **Success Criteria**: `ConversationAgent` is a pure orchestrator, and all concept-specific logic is isolated in `ConceptManager`, following the "Incremental Extraction" model.

1.  **Main Screen UI Refinement - High-Tech Interface Upgrade**
    -   **Task**: Overhaul the main screen UI with a more sophisticated, "Star Trek-style" aesthetic, including unified goal lists, geometric dividers, and transparency effects.
    -   **Reason for Deferral**: Cosmetic enhancement. The current UI is functional for the MVP.

2.  **Conversation History Enhancement**
    -   **Task**: Create a dynamic, animated conversation history panel with a toggle between current and previous sessions.
    -   **Reason for Deferral**: A "nice-to-have" feature. Basic history is sufficient for the MVP.

3.  **Main Screen Goal Display Enhancement**
    -   **Task**: Add extra details to the goal display on the main screen, such as last-updated timestamps.
    -   **Reason for Deferral**: Minor enhancement.

4.  **Check-in Session Control**
    -   **Task**: Add a "Start New Check-in" button and implement logic to choose between continuing a session or starting fresh.
    -   **Reason for Deferral**: The default behavior (always continuing the last session) is sufficient for the initial MVP.

5.  **Session Resume Enhancement**
    -   **Task**: Improve the context preservation and theme visibility when resuming conversations across app sessions.
    -   **Reason for Deferral**: The current session handling is adequate for initial testing.

6.  **User Experience Refinements & Launch Readiness**
    -   **Task**: Implement a simple onboarding flow, add helpful voice command hints, and add more robust error handling.
    -   **Reason for Deferral**: These are polish items best addressed after the core functionality is validated with a real user.
