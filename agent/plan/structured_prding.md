# A 4-Prompt Framework for Generating Actionable PRDs

This framework guides an AI from a high-level concept to a detailed, developer-ready specification.

---

### **Prompt 1: Define the Core Objective**

**Purpose:** To isolate a single task and establish its fundamental "why" from a user's point of view. This ensures the subsequent analysis is grounded in user value.

**The Prompt:**
> "Select a single, specific task from `[Source Document, e.g., @agent/planning/MVP_JULY_LAUNCHv1.md]` and define its core purpose in a single sentence, focusing on the primary value it delivers to the end-user."

---

### **Prompt 2: Establish Context and Scope**

**Purpose:** To force an analysis of the existing system and define the task's boundaries. This prevents planning in a vacuum and grounds the PRD in the current reality of the codebase.

**The Prompt:**
> "For the selected task, analyze the existing application. Identify:
> 1.  The primary **User Story** (using the format: "As a [user type], I want to [action] so that [benefit]").
> 2.  The key **technical components** (files, classes, services, UI elements) that will likely be impacted by this change.
> 3.  Any **dependencies** this task has on other features, whether existing or planned."

---

### **Prompt 3: Specify Success and Identify Risks**

**Purpose:** To move from the "what" to the "how" by defining concrete, measurable outcomes and anticipating problems. This is the core of architectural consideration.

**The Prompt:**
> "Now, detail the requirements and constraints:
> 1.  List the specific, testable **Acceptance Criteria** as a checklist. What must be true for this task to be considered complete?
> 2.  What are the key **architectural decisions or trade-offs** to be made? (e.g., create a new component vs. modify an existing one, data flow changes, state management strategy).
> 3.  Identify potential **risks, edge cases, or non-functional requirements** (e.g., error handling, performance impact, UI responsiveness, accessibility)."

---

### **Prompt 4: Synthesize the PRD**

**Purpose:** To assemble all the analyzed information into a final, structured document that a coding agent can directly work from.

**The Prompt:**
> "Synthesize the information from the previous steps into a concise, actionable PRD. The document should be formatted in Markdown and include these sections:
>
> *   **1. Feature:** (A clear, descriptive title)
> *   **2. Background & User Story:** (Why this is important and who it's for)
> *   **3. Acceptance Criteria:** (The checklist for completion)
> *   **4. High-Level Technical Approach:** (Summary of the architectural decisions and components involved)
> *   **5. Key Questions & Risks to Address:** (The list of open questions or risks for the implementing agent to resolve)"