# WishCreationManager - Implementation PRD
# 📋 FUNCTION: Clean, actionable specification for implementing wish creation functionality

## Background

Users need to create new wishes through natural conversation after check-in. The TransitionChain can route to WISH_COLLECTION, but ConversationAgent cannot handle it because WishCreationManager doesn't exist.

## Requirements

### Core Functionality
- Generate initial wish from check-in themes using LLM
- Allow conversational refinement through voice feedback
- Save completed wish to database slot
- Return to core loop when complete

### Integration Points
- **Entry:** ConversationAgent.navigateCoreLoopPhase() WISH_COLLECTION case
- **LLM:** BrainService.generateSimpleText() (already exists)
- **Database:** WishUtilities.saveWishToSlot() (already exists)
- **State:** AgentCortex state management

---

## Implementation Specification

### 1. Data Structures

```kotlin
data class WishCreationResult(
    val speechResponse: String,
    val shouldContinue: Boolean,
    val isComplete: Boolean = false
)

data class WishCreationState(
    val isActive: Boolean = false,
    val currentWish: String? = null,
    val stage: String = "generating"
)
```

### 2. WishCreationManager Class

**Location:** `app/src/main/java/com/example/voxmanifestorapp/ui/agent/wishcreation/WishCreationManager.kt`

```kotlin
class WishCreationManager(
    private val wishUtilities: WishUtilities,
    private val brainService: BrainService
) {
    suspend fun processWishCreation(
        themes: List<ConversationalTheme>, 
        userInput: String?, 
        currentState: WishCreationState
    ): WishCreationResult
    
    private suspend fun generateInitialWish(themes: List<ConversationalTheme>): String
    private suspend fun refineWish(currentWish: String, userFeedback: String): String
}
```

### 3. State Machine Logic

**Stages:** "generating" → "refining" → "validating"

- **generating:** Create initial wish from themes, present to user
- **refining:** Take user feedback, improve wish, repeat until satisfied
- **validating:** Get final yes/no confirmation, save if yes

### 4. LLM Prompts

**Initial Generation:**
```
Analyze these conversation themes and create a specific, present-tense wish:
[themes]

Requirements:
- Present tense, positive language
- Specific and actionable
- Emotionally resonant
- Based on the themes provided
```

**Refinement:**
```
Current wish: [currentWish]
User feedback: [userFeedback]

Refine the wish based on feedback while maintaining:
- Present tense, positive language
- Specificity and clarity
- Core intention from original wish
```

---

## Integration Requirements

### 1. AgentCortex Updates

Add to `AgentCortex.kt`:
```kotlin
private val _wishCreationState = MutableStateFlow(WishCreationState())
val wishCreationState: StateFlow<WishCreationState> = _wishCreationState.asStateFlow()

fun updateWishCreationState(state: WishCreationState) {
    _wishCreationState.value = state
}
```

### 2. ConversationAgent Updates

Add to `ConversationAgent.kt`:
```kotlin
private val wishUtilities = WishUtilities(repository)
private val wishCreationManager = WishCreationManager(wishUtilities, brainService)

// Add to navigateCoreLoopPhase() switch statement:
ConversationPhase.WISH_COLLECTION -> {
    launchWishCreation(actionPlan.themes)
}

private suspend fun launchWishCreation(themes: List<ConversationalTheme>) {
    // Implementation here
}
```

---

## Implementation Tasks

### Task 1: Create Core Infrastructure
1. Create `/wishcreation/` directory
2. Implement `WishCreationManager` class
3. Define data structures in appropriate file

### Task 2: Add State Management
1. Add `WishCreationState` to `AgentCortex`
2. Add update methods for state management

### Task 3: Integrate with ConversationAgent
1. Add `WISH_COLLECTION` case to `navigateCoreLoopPhase()`
2. Implement `launchWishCreation()` method
3. Handle conversation flow and state transitions

### Task 4: Test Integration
1. Test theme-to-wish generation
2. Test refinement conversation loop
3. Test database save functionality
4. Test return to core loop

---

## Success Criteria

1. **Functional Flow:** Check-in → TransitionChain → WISH_COLLECTION → WishCreationManager → Saved wish
2. **Conversational:** User can refine wish through natural voice feedback
3. **Integration:** Seamless transition back to core loop after completion
4. **Data Persistence:** Wishes saved correctly to database slots

---

## Notes

- Keep implementation simple - this is just a state machine with LLM calls
- Reuse existing patterns from CheckInSystem and CommandMode
- Focus on MVP functionality, avoid over-engineering
- Use existing error handling patterns from the codebase
