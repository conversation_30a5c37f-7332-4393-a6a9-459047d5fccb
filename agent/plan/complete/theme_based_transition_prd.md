# Check-In Phase: Theme-Based Transition - PRD

## Executive Summary

**Problem**: The current Check-In phase transitions abruptly to the next Core Loop phase without giving users visibility into the themes extracted from their conversation or allowing them to choose which theme to focus on.

**Solution**: Enhance the Check-In phase with a proper TRANSITION stage that presents conversation themes to the user and facilitates collaborative focus selection before proceeding to the next phase.

**Impact**: Users will experience a more natural conversational flow, feel more heard and understood, and have greater agency in directing their manifestation journey.

## Document Context

This PRD is a detailed implementation plan for **Phase 2 (Enhanced Transitions)** of the broader Conversation Deepening initiative. It sits in the following document hierarchy:

```
conversation_deepening_prd.md (Parent document)
   ↓
theme_based_transition_prd.md (This document)
   ↓
conversation_deepening_tasks.md (Specific implementation tasks)
```

While the parent document outlines the strategic vision across all phases, this document focuses specifically on the tactical details of implementing the theme-based transition feature that bridges the OPENING and TRANSITION stages within the Check-In phase.

## Background

The Check-In phase currently has two conceptual stages:
1. **OPENING**: Free-form conversation, theme extraction, transition determination
2. **TRANSITION**: Bridge to the next Core Loop phase

However, the current implementation doesn't properly distinguish between these stages. When the DialogueChain determines it's time to transition, it jumps directly to the next phase without leveraging the extracted theme data or involving the user in focus selection.

## Goals

1. Implement a proper two-stage flow within the Check-In phase
2. Present extracted conversation themes to the user
3. Suggest appropriate next steps based on extracted themes
4. Process user's natural language response to determine next action
5. Use the theme context to inform the next Core Loop phase
6. Maintain architectural separation of responsibilities

## Non-Goals

1. Changing the theme extraction logic (already implemented in Chain 1B)
2. Modifying the transition decision logic (already implemented in Chain 1)
3. Redesigning the broader Core Loop architecture
4. Creating new input types or specialized data structures when existing ones can be used, though this needs further analysis

## Architectural Overview

### Core Hierarchical Structure

- **Core Loop**: The highest level conversation cycle in the app
- **Check-In Sub-Loop**: A specialized conversation context (Phase 1) within the Core Loop
- **Check-In Stages**: Internal stages within the Check-In sub-loop:
  1. **OPENING**: Free-form conversation, theme extraction, and transition determination
  2. **TRANSITION**: Collaborative theme selection and bridge to next phase

### Component Responsibilities

1. **DialogueChain.processTurn()**
   - Processing single conversation turns (already implemented)
   - Determining if transition is needed (already implemented)
   - Extracting themes (already implemented)
   - Returning a comprehensive DialogueChainResponse

2. **ConversationAgent.progressCheckIn()**
   - Orchestrating the Check-In flow
   - Handling the OPENING stage
   - Detecting when to enter the TRANSITION stage
   - Calling handleThemeBasedTransition() when ready to transition
   - NOT directly calling handleCoreLoopTransition()

3. **ConversationAgent.handleThemeBasedTransition()** (NEW)
   - Managing the TRANSITION stage
   - Presenting themes to the user
   - Suggesting next steps based on themes
   - Setting appropriate DialogueState for user response
   - Awaiting user input

4. **ConversationAgent.handleTransitionResponse()** (NEW)
   - Processing user's natural language response in the TRANSITION stage
   - Interpreting user intent through LLM processing
   - Determining appropriate next Core Loop phase
   - Only proceeding to next phase after intent processing is complete

5. **ConversationAgent.progressCoreLoop()**
   - Transitioning from Check-In to another Core Loop phase
   - Using theme context from CheckInState.activeThemes

### Data Flow

```
DialogueChain.processTurn()
  ↓ returns DialogueChainResponse with transitionDecision == true
ConversationAgent.progressCheckIn()
  ↓ updates CheckInState to TRANSITION stage
ConversationAgent.handleThemeBasedTransition()
  ↓ presents themes, suggests next steps, and awaits user input
User responds with natural language intent
  ↓ input captured via DialogueState.ExpectingInput
ConversationAgent.handleTransitionResponse()
  ↓ processes user intent and prepares for Core Loop transition
  ↓ calls progressCoreLoop() with theme context in CheckInState
progressCoreLoop()
  ↓ uses theme context from CheckInState to inform next phase
Next Core Loop Phase
```

## Implementation Guidelines

### 1. Stage Management [COMPLETE]

- Use CheckInState.currentStage to track whether we're in OPENING or TRANSITION
- Only progress to TRANSITION stage when DialogueChain indicates transition
- Only exit Check-In phase after user selects a theme

### 2. Theme Presentation [COMPLETE]

- Format themes in a clear, natural language summary
- Present 3-5 themes maximum to avoid overwhelming the user
- Include a suggested next action based on identified themes
- If no meaningful themes were extracted, use a generic transition approach

### 3. User Intent Processing

- Process the user's natural language response to determine their desired next action
- Use LLM to interpret the user's intent in context of the presented themes
- Select the appropriate next Core Loop phase based on this interpretation
- Provide confirmation feedback before transitioning
- Further analysis needed on whether to use existing VoxInputType or create a specialized one

### 4. Context Handoff

- Use existing CheckInState.activeThemes to pass theme context to the next phase
- Store interpreted user intent in CoreLoopState.currentUnderstanding
- This approach avoids creating unnecessary new data structures

## Required Changes

1. **CheckInStage.TRANSITION Usage**
   - Use the existing CheckInStage.TRANSITION enum value which is already defined but not utilized
   - Update CheckInState.currentStage when transitioning between stages

2. **Existing Input Type Usage**
   - Use VoxInputType.BRAIN_RESPONSE for theme selection input
   - Distinguish processing based on CheckInState.currentStage rather than input type

3. **Core Loop Integration**
   - Update CoreLoopState.currentUnderstanding with selected theme information
   - Use this to personalize the next phase of the Core Loop

## Success Criteria

1. Check-In properly transitions through both stages (OPENING → TRANSITION → Next Phase)
2. User sees a summary of conversation themes
3. Agent suggests relevant next steps based on themes
4. User's natural language response is correctly interpreted
5. Theme context influences the subsequent phase
6. No direct calls to handleCoreLoopTransition from progressCheckIn

## Implementation Phases

### Phase 1: Architecture Updates (In Progress)
- Update ConversationAgent.progressCheckIn() to detect transition and change stages
- Prepare for theme-based transition by changing control flow
- Use existing VoxInputType.BRAIN_RESPONSE for theme selection to avoid unnecessary additions

### Phase 2: Theme Presentation
- Implement handleThemeBasedTransition() method in ConversationAgent
- Create formatThemesForPresentation() helper function
- Implement fallback logic to handleTransitionStage() for cases with no themes
- Update message generation to highlight extracted themes and suggest next phase

### Phase 3: Theme Selection Processing
- Enhance handleCoreLoopResponse() to check for CheckInState.currentStage
- Add handleThemeSelection() method to process theme selection
- Implement matchUserResponseToTheme() for reliable theme matching
- Add confirmation feedback before transition

### Phase 4: Context Handoff
- Store selected theme in CoreLoopState.currentUnderstanding
- Create determineNextPhase() method to select appropriate next phase
- Update progressCoreLoop() to recognize and use theme context

## Future Enhancements (Post-MVP)

1. Use LLM to generate more personalized theme summaries
2. Enhance theme matching with more sophisticated NLP
3. Add visual theme presentation for accessibility
4. Track theme selection history to improve future recommendations

## Implementation Readiness

### Core Components Status
- **DialogueChain.processTurn()**: ✅ Complete (Chain 1B already extracts themes)
- **ConversationAgent.progressCheckIn()**: 🔄 Ready for update
- **CheckInState.currentStage**: ✅ Available but not properly utilized
- **handleCoreLoopResponse()**: ✅ Exists but needs enhancement

### Key Technical Decisions
- We will reuse VoxInputType.BRAIN_RESPONSE rather than adding a new input type
- We will use CoreLoopState.currentUnderstanding to store theme context
- We will leverage the existing handleTransitionStage() as a fallback

### Implementation Dependencies
- No new libraries or external dependencies required
- All necessary components are already in place
- Implementation can be done with modifications to existing files

### Open Questions
1. Is storing theme context in CoreLoopState.currentUnderstanding appropriate, or should we add a dedicated field?
2. Should we implement a formal TransitionContext class, or is passing the theme information in the existing state sufficient?
3. Is using the existing handleTransitionStage() as a fallback for cases with no themes appropriate? 