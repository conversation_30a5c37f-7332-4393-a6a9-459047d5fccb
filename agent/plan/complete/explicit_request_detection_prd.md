# Explicit Function Request Detection - Emergency Ejection PRD

## Background and Motivation

The DialogueChain transition evaluation is incorrectly triggering transitions when no conversation has occurred, and lacks proper detection of explicit user requests for specific agent functions. This creates two critical issues:

1. **Empty History Problem**: LLM transitions immediately when no conversation exists
2. **Missing Request Detection**: User requests for specific functions (wishes, concepts, affirmations) are not being detected

## Problem Statement

**Current Issue**: The transition evaluation prompt provides insufficient context for the LLM to make informed decisions, especially when:
- No conversation history exists yet
- User explicitly requests specific agent capabilities
- LLM has no guidance on what constitutes a valid transition trigger

**Root Cause**: The transition prompt lacks:
- Proper handling of empty conversation states
- Clear criteria for explicit function requests
- Context about available agent processes

## Agent Process Context for Transition Decisions

### Core MVP Processes (from Integration PRD)

| Process | Function | User Request Patterns | Target Phase |
|---------|----------|----------------------|--------------|
| **Wish Management** | Define/manage up to 5 core wishes | "work on wishes", "create goals", "review manifestations" | `WISH_COLLECTION` |
| **Concept Building** | Present/desired state articulation | "present state", "desired state", "where I am vs want to be" | `PRESENT_STATE_EXPLORATION` / `DESIRED_STATE_EXPLORATION` |
| **Affirmations** | Personalized affirmation sessions | "affirmations", "positive statements", "manifestation practice" | `AFFIRMATION_PROCESS` |

### Emergency Ejection Criteria

**Primary Transition Trigger**: Explicit function requests only
- User explicitly asks to work on wishes, concepts, or affirmations
- User indicates they want to move beyond check-in to specific work
- User expresses readiness for structured processes

**Invalid Transition Triggers**:
- Empty or minimal conversation history
- General conversation without specific function requests
- Check-in still providing value and connection

## High-level Task Breakdown

### Task 1: Fix Empty History Handling ⚠️ **IMMEDIATE**
- Modify `buildTransitionPrompt()` to detect empty conversation history
- Set `historyText` to "No conversation has yet occurred" when history is empty
- Ensure LLM understands this means "do not transition"

### Task 2: Add Explicit Request Detection ⚠️ **IMMEDIATE**
- Enhance transition prompt with agent process catalog
- Add clear criteria for detecting function requests
- Provide examples of valid vs invalid transition triggers

### Task 3: Update Transition Logic ⚠️ **IMMEDIATE**
- Ensure transition only occurs for explicit function requests
- Maintain check-in focus unless user specifically requests otherwise
- Add reasoning validation for transition decisions

### Task 4: Test Request Detection 🔄 **NEXT**
- Validate detection of wish management requests
- Validate detection of concept building requests  
- Validate detection of affirmation requests
- Ensure false positives are minimized

## Implementation Details

### Enhanced Transition Prompt Structure

```kotlin
val historyText = if (history.isEmpty()) {
    "No conversation has yet occurred - this is the very beginning of the check-in."
} else {
    history.takeLast(10).joinToString(separator = "\n\n") { entry ->
        val speaker = when (entry.speaker) {
            Speaker.User -> "User"
            Speaker.Agent -> "Coach"
            else -> entry.speaker.toString()
        }
        "$speaker: ${entry.content}"
    }
}
```

### Explicit Request Detection Criteria

**Function Request Patterns**:
- **Wishes**: "work on wishes", "create goals", "manifestations", "what I want"
- **Concepts**: "present state", "desired state", "where I am", "gap analysis"
- **Affirmations**: "affirmations", "positive statements", "manifestation practice"
- **General**: "what's next", "move on", "ready for", "let's work on"

**Transition Decision Logic**:
1. **No History**: Always return `shouldTransition: false`
2. **Explicit Request Detected**: Return `shouldTransition: true` with specific reasoning
3. **General Check-in**: Return `shouldTransition: false` to continue conversation

## Success Criteria

### Functional Requirements
- [ ] Empty conversation history never triggers transition
- [ ] Explicit function requests are accurately detected
- [ ] Check-in conversations continue until explicit request
- [ ] Transition reasoning clearly identifies detected request

### Technical Validation
- [ ] `buildTransitionPrompt()` handles empty history properly
- [ ] LLM receives clear guidance on transition criteria
- [ ] Process catalog embedded in transition evaluation
- [ ] False positive transitions eliminated

### User Experience
- [ ] Check-in feels natural and unhurried
- [ ] User requests for specific functions are honored immediately
- [ ] No premature transitions from valuable check-in conversations

## Risk Mitigation

### Immediate Fixes
- **Empty History Guard**: Prevent transitions when no conversation exists
- **Clear Criteria**: Provide explicit guidance to LLM on valid transitions
- **Process Context**: Embed agent capabilities in transition evaluation

### Fallback Strategy
- **Conservative Approach**: Default to continuing check-in unless clear request
- **Reasoning Validation**: Require specific reasoning for transition decisions
- **Manual Override**: Allow hard rules to override LLM decisions

---

**Priority**: IMMEDIATE - This is causing premature transitions and poor user experience in the check-in phase. The fix should restore proper check-in flow while enabling intelligent function request detection. 