# PRD: Conversation History Context Enhancement

## Background and Motivation

The current implementation of the DialogueChain system limits the conversation history included in prompts to the LLM:
- `buildTransitionPrompt()`: Only includes the last 10 messages
- `buildResponseWithThemesPrompt()`: Only includes the last 5 messages
- `buildStrategySelectionWithThemesPrompt()`: Only includes the last 5 messages

These limitations create potential issues:
1. The agent loses context from earlier in the conversation
2. References to topics mentioned earlier might be forgotten
3. User experience degrades when the agent doesn't remember previous context
4. The agent's ability to identify long-term themes and patterns is compromised

Reviewing both the `checkin_context.md` and `core_loop_context.md` documents, it's clear that maintaining conversational context is critical to the VoxManifestor experience. The Core Loop specifically calls for "remembering & adapting" based on past interactions.

## Key Challenges and Analysis

### Current Implementation Issues

When examining the code that builds prompts, we consistently see `takeLast(n)` being used:

```kotlin
// In buildTransitionPrompt()
val historyText = history.takeLast(10).joinToString(...)

// In buildResponseWithThemesPrompt() 
val historyText = initialTurnContext.historySnapshot.takeLast(5).joinToString(...)

// In buildStrategySelectionWithThemesPrompt()
val historyText = initialTurnContext.historySnapshot.takeLast(5).joinToString(...)
```

### Token Consumption Considerations

While including the full conversation history provides better context, we must consider token limits:
- Google Gemini models have context windows of 32K tokens
- Very long conversations could approach this limit
- Response time increases with prompt size

### Balance of Completeness vs. Performance

We need to balance:
1. Having sufficient context for meaningful responses
2. Optimizing token usage and response time
3. Ensuring critical information is never lost

## Proposed Solution: Token-Aware History Management

We will implement a lightweight solution that maintains the same data structure as the existing conversationHistory while intelligently managing token limits. This approach avoids introducing new types or additional processing in the receiving functions.

### Key Component: HistoryTokenManager

A single utility object with a minimalist API that:
- Takes a List<ConversationEntry> as input
- Returns a List<ConversationEntry> as output (the same type used by AgentCortex)
- Internally manages token counts and truncation when necessary
- Provides optional logging of token usage when requested
- Seamlessly integrates with existing code with minimal changes

## High-level Task Breakdown

1. **Create HistoryTokenManager**:
   - Implement `HistoryTokenManager.kt` with simple token estimation and truncation functions
   - Ensure the API returns the same List<ConversationEntry> type used elsewhere
   - Success criteria: Functions accurately estimate tokens and truncate without changing return types

2. **Modify Prompt Builders**:
   - Update all prompt builder functions to use HistoryTokenManager instead of `takeLast(n)`
   - Success criteria: All prompt builders use token-aware history management

3. **Add Optional Logging**:
   - Add logging capability that can be enabled/disabled
   - Success criteria: Logs provide visibility into token usage when needed

4. **Update Documentation**:
   - Document the new utility class
   - Success criteria: Documentation accurately reflects new implementation

## Detailed Implementation Plan

### 1. HistoryTokenManager Implementation

```kotlin
/**
 * Utility for managing conversation history with token awareness.
 * 
 * Key design principle: Returns the same data type (List<ConversationEntry>) as input
 * to seamlessly integrate with existing code.
 */
object HistoryTokenManager {
    private const val APPROXIMATE_CHARS_PER_TOKEN = 4
    private const val DEFAULT_TOKEN_LIMIT = 25000
    
    /**
     * Returns a token-aware subset of conversation history that fits within limits.
     * Maintains the same List<ConversationEntry> type as the input.
     * 
     * @param history The full conversation history
     * @param tokenLimit Maximum number of tokens to allow
     * @param logger Optional logging function
     * @return A List<ConversationEntry> that fits within token limits
     */
    fun getTokenSafeHistory(
        history: List<ConversationEntry>,
        tokenLimit: Int = DEFAULT_TOKEN_LIMIT,
        logger: ((String, StatusColor) -> Unit)? = null
    ): List<ConversationEntry> {
        if (history.isEmpty()) {
            return history
        }
        
        // Calculate total tokens
        val historyWithTokens = history.map { entry ->
            entry to estimateTokens(entry.content)
        }
        
        val totalTokens = historyWithTokens.sumOf { it.second }
        
        // If under limit, return full history
        if (totalTokens <= tokenLimit) {
            logger?.invoke("🔤 Using full history: $totalTokens tokens", StatusColor.Default)
            return history
        }
        
        // If over limit, truncate from oldest entries
        var currentTokens = 0
        val safeHistory = mutableListOf<ConversationEntry>()
        
        // Process from newest to oldest, keeping only what fits
        for ((entry, tokens) in historyWithTokens.asReversed()) {
            if (currentTokens + tokens <= tokenLimit) {
                safeHistory.add(0, entry)  // Add to beginning to maintain order
                currentTokens += tokens
            } else {
                break
            }
        }
        
        logger?.invoke("🔤 History truncated: $currentTokens tokens (removed ${history.size - safeHistory.size} messages)", StatusColor.Pause)
        
        return safeHistory
    }
    
    /**
     * Estimates tokens using character-based approximation.
     */
    private fun estimateTokens(text: String): Int {
        return text.length / APPROXIMATE_CHARS_PER_TOKEN
    }
}
```

### 2. Integration in Prompt Builders

```kotlin
// In DialogueChain.kt, buildTransitionPrompt()
private fun buildTransitionPrompt(history: List<ConversationEntry>, metrics: UserEngagementMetrics): String {
    val systemInstructions = Prompts.systemInstructions
    
    // Replace history.takeLast(10) with token-aware version
    val safeHistory = HistoryTokenManager.getTokenSafeHistory(history, logger = logger)
    val historyText = safeHistory.joinToString(separator = "\n\n") { entry ->
        val speaker = when (entry.speaker) {
            Speaker.User -> "User"
            Speaker.Agent -> "Coach"
            else -> entry.speaker.toString()
        }
        "$speaker: ${entry.content}"
    }
    
    // Rest of the method remains the same...
}

// Similarly update buildResponseWithThemesPrompt() and buildStrategySelectionWithThemesPrompt()
```

## Success Criteria

- Agent successfully refers to information mentioned earlier in longer conversations
- No significant degradation in response times
- Token usage remains within safe limits
- User perceives the agent as having better memory and contextual understanding
- Integration requires minimal code changes to existing functions
- No new data types or processing requirements in receiving functions

## Project Status Board

- [X] Review current prompt builder implementations
- [X] Create `HistoryTokenManager.kt` with token-aware history management
- [X] Modify prompt builders to use HistoryTokenManager instead of takeLast(n)
- [X] Add optional token usage logging
- [ ] Update documentation
- [ ] Test with varied conversation lengths (5, 20, 50+ messages)

## Executor's Feedback or Assistance Requests

*Implementation of `HistoryTokenManager.kt` and its integration into `DialogueChain.kt` is complete. The system now uses token-aware history truncation for prompts sent to the LLM. Logging for token usage and truncation events is included. The next steps involve documentation and testing.*
