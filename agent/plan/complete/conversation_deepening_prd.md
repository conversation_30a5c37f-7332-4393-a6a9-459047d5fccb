# Check-In Conversation Deepening - MVP PRD

## Executive Summary

**Problem**: When users share complex responses with multiple themes, the agent asks one follow-up question and moves on, leaving valuable information unexplored.

**Solution**: Add theme exploration capabilities to the check-in system, starting with invisible data collection and progressing to active theme-based conversation.

**Impact**: Users feel truly heard as the agent demonstrates memory and genuine interest in all their concerns, building structured knowledge for manifestation work.

**IMPLEMENTATION STATUS**: Phase 1 (Silent Collection) has been successfully implemented. Phase 2 (Enhanced Transitions) is the current development focus.

## Document Hierarchy

This PRD serves as the parent document for the Conversation Deepening initiative, with related documents providing more detailed implementation guidance:

```
conversation_deepening_prd.md
   ↓ Strategic vision for all phases
   ├── theme_based_transition_prd.md 
   │    ↓ Detailed implementation plan for Phase 2 (Enhanced Transitions)
   │    └── conversation_deepening_tasks.md
   │         ↓ Specific tasks to implement Phase 2 features
   └── [future documents for Phase 3]
```

- **This document (conversation_deepening_prd.md)**: High-level strategic overview of the entire conversation deepening initiative across all phases
- **theme_based_transition_prd.md**: Focused tactical PRD specifically for implementing Phase 2 (Enhanced Transitions)
- **conversation_deepening_tasks.md**: Actionable task list for implementing the features specified in the theme-based transition PRD

## MVP Strategy: Theme-Enhanced Check-In

### Phase 1: Silent Collection (COMPLETED) ✓
- **Key Innovations**: 
  - Extract themes silently from user messages via Chain 1B
  - Store themes in agent memory for context building
  - Make response generation and strategy selection theme-aware
- **User Experience**: No change to conversation flow, but responses are more contextually relevant and show greater memory of earlier points.

### Phase 2: Enhanced Transitions (CURRENT FOCUS) →
- **Key Innovations**:
  - Two-Stage Check-In Flow: Clear separation between OPENING and TRANSITION stages
  - Theme Presentation: Surfacing extracted themes to the user at transition moment
  - Contextual Next Step Suggestion: Agent suggests next steps based on identified themes
  - Natural Language Intent Processing: Interpreting user's desired next action
  - Rich Context Handoff: Passing theme context to the next Core Loop phase
- **User Experience**: 
  - Users experience a natural bridge between free-form conversation and structured manifestation work
  - Users see that the agent has understood the key themes of their sharing
  - Users express in natural language what they want to focus on next
  - Agent intelligently interprets user intent and transitions to appropriate next steps
  - Subsequent phases benefit from deeper context about user priorities

### Phase 3: Theme-Aware Core Loop (FUTURE)
- **Key Innovations**: Integrate theme awareness across entire manifestation journey
- **User Experience**: Every phase of the Core Loop refers back to and builds upon identified themes.

## Architectural Design

### Phase 1: Silent Collection (IMPLEMENTED) ✓

1. **Theme Extraction**:
   - Added Chain 1B to extract themes after user input
   - Created theme extraction prompts for LLM
   - Implemented theme aggregation logic

2. **Theme Storage**:
   - Extended CheckInState with activeThemes list
   - Added theme updating to AgentCortex
   - Implemented theme persistence across turns

3. **Theme-Aware Processing**:
   - Made strategy selection consider themes
   - Enhanced response generation with theme awareness
   - Maintained error handling with theme fallbacks

### Phase 2: Enhanced Transitions (CURRENT FOCUS) →

1. **Two-Stage Check-In Flow**:
   - **OPENING Stage**: Free-form conversation with silent theme extraction (already implemented)
   - **TRANSITION Stage**: Bridge to next phase with theme presentation and selection
   - Clear separation of responsibilities between determining transition (DialogueChain) and executing transition (ConversationAgent)

2. **Theme Presentation**:
   - Format themes in natural language for presentation
   - Generate transition messages that include theme summary
   - Present 3-5 key themes maximum to avoid overwhelming the user
   - Include fallback for cases with no meaningful themes

3. **Theme Selection Processing**:
   - Capture user theme selection through specialized input type
   - Match selection text to appropriate theme
   - Provide confirmation feedback
   - Generate conversation summary for context

4. **Context Handoff**:
   - Package selected theme and context for next phase
   - Use theme content to influence next phase determination
   - Enable personalization of next phase based on theme

### Component Responsibilities

1. **DialogueChain**: 
   - Determine when to transition (Chain 1)
   - Extract themes from conversation (Chain 1B)
   - Return comprehensive DialogueChainResponse

2. **ConversationAgent**:
   - Orchestrate overall Check-In flow
   - Manage stage transitions (OPENING → TRANSITION)
   - Present themes and collect user selection
   - Handle transitions to next Core Loop phase

3. **AgentCortex**:
   - Maintain conversational state including themes
   - Track Check-In stage (OPENING vs TRANSITION)
   - Provide state persistence across the application

## Success Metrics

### Phase 1 (Complete) ✓
- Themes successfully extracted from user messages
- Strategy selection and response generation use theme data
- No negative impact on conversation performance

### Phase 2 (Current Focus) →
- Check-In properly transitions through both stages
- User can see theme summary at appropriate moment
- User can select which theme to focus on
- Selected theme influences the next Core Loop phase
- Conversation feels cohesive with natural transitions

## Implementation Timeline

- **Phase 1**: Completed
- **Phase 2**: Currently in development
  - Architecture Updates (in progress)
  - Theme Presentation
  - Theme Selection Processing
  - Context Handoff
- **Phase 3**: Future development

## Future Considerations

- **Cross-Session Theme Persistence**: Remember themes across app sessions
- **Theme Evolution Tracking**: Observe how themes change over time
- **Advanced Theme Interconnections**: Identify relationships between themes
- **Theme-Based Affirmation Generation**: Create personalized affirmations
- **Theme Visualization**: Add visual representation of themes for multi-modal experience 