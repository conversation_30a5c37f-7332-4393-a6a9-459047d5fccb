# PRD: ConceptManager Extraction (Definitive Architectural Model)

## 1. Feature: Concept Logic Extraction from ConversationAgent

**Background & User Story:**
This is the final step in a major refactoring effort to modularize the `ConversationAgent`. The agent currently contains concept-building processing logic that could be extracted to improve maintainability while preserving the sophisticated integration patterns already in place.

As a developer, I want to extract concept-building processing logic into a dedicated `ConceptManager` following the established "frontal cortex" pattern (like CoreLoopManager), ensuring the `ConversationAgent` retains its orchestration role while improving code organization and testability.

## CRITICAL ANALYSIS: Missing Architectural Considerations

### 1.1. Core Loop Integration Complexity
**OVERLOOKED**: The PRD treats concept building as an isolated system, but the codebase reveals deep integration with the Core Loop architecture:

- **Phase-Based Routing**: `ConversationAgent.launchConceptScreenForPhase()` shows concept building is triggered by specific Core Loop phases (`PRESENT_STATE_EXPLORATION`, `DESIRED_STATE_EXPLORATION`)
- **Context Preservation**: Core Loop state must be preserved when transitioning to concept screen and restored when returning
- **Navigation Coordination**: The agent manages complex navigation between MainScreen → ConceptScreen → MainScreen with state continuity

### 1.2. Existing Communication Architecture
**CONFIRMED WORKING**: The current system uses sophisticated communication patterns that work correctly and follow the Check-In system model:

- **AgentCortex UiIntent System**: Provides structured UI→Agent communication via `UiIntent.NotifyConceptScreenExit`, `UiIntent.SendResponse`
- **ConversationAgent.observeConceptViewModel()**: Establishes bidirectional communication with ConceptViewModel for state synchronization
- **Voice Input Processing**: Each utterance immediately triggers `handleUserResponse()` → `addToHistory()` following Check-In pattern
- **Response Button Processing**: Triggers agent processing of accumulated conversation history, not text submission
- **Direct ViewModel Access**: `currentConceptViewModel` allows agent to control visual interface directly

### 1.3. State Management Complexity
**OVERLOOKED**: The PRD underestimates the state management complexity:

- **ConceptActionState**: Existing state machine for concept building progression (`INITIATE`, `QUESTION_RESPONSE`, etc.)
- **ConversationPlan**: Brain-generated plans stored in AgentCortex that guide concept conversations
- **DataLoadingState**: ConceptViewModel loading states that agent monitors to determine readiness
- **Screen Lifecycle**: Complex initialization and cleanup when entering/exiting concept screen

### 1.4. Tool Library and Prompt Engineering
**OVERLOOKED**: The PRD doesn't address the sophisticated prompt engineering system:

- **ConceptToolLibrary**: Complex tool system for concept building that requires deep integration
- **getWorkingPrompt()**: Dynamic prompt generation based on current concept state
- **ConceptBuildingContext**: Rich context object that bridges ConceptViewModel and BrainService
- **Tool Execution**: `QuestionTool.execute()` and other tools that require coordination

### 1.5. Error Handling and Recovery
**OVERLOOKED**: The PRD doesn't address existing error handling patterns:

- **Centralized Error Handling**: `ConversationAgent.handleError()` provides consistent error management
- **Conversation Scope Management**: `conversationScope` provides clean cancellation and cleanup
- **State Recovery**: Complex logic for recovering from failures and maintaining conversation continuity

## 2. REVISED Architectural Analysis: Current vs. Proposed

### 2.1. ACTUAL Current Architecture (Not "God Agent")

**CORRECTION**: The codebase analysis reveals the ConversationAgent has already been significantly modularized (reduced from ~3000 to 1849 lines). The current architecture is:

```
+--------------------------------------------------------------------+
| ConversationAgent (Orchestrator - 1849 lines)                     |
|--------------------------------------------------------------------|
| - Delegates to specialized modules (commands/, coreloop/, etc.)    |
| - Maintains control of external interfaces (TTS, Navigation)       |
| - Orchestrates concept building via observeConceptViewModel()      |
| - Uses AgentCortex UiIntent system for UI communication           |
+------------------+-----------------+----------------+--------------+
                   |                 |                |
                   v                 v                v
        +----------------+ +---------------+ +--------------------+
        | Specialized    | | AgentCortex   | | ConceptViewModel   |
        | Modules        | | (State Hub)   | | (Screen Control)   |
        +----------------+ +---------------+ +--------------------+
```
**Reality:** This is already a well-architected system with clear separation of concerns.

### 2.2. CONFIRMED: Current Architecture Already Works Well

**INSIGHT**: Analysis of the actual codebase reveals the current system already follows excellent patterns:

```
CURRENT WORKING SYSTEM (Follows Check-In Pattern):
User Voice Input → handleUserResponse() → addToHistory() → AgentCortex.conversationHistory
                                                                    ↓
Response Button → processUserResponse() → getConceptBuildingContext() → getNextBrainDecision()
                                                                    ↓
                    speak() ← ConceptManager Processing ← BrainService
                      ↓
              addToHistory(Agent response)
```

**Analysis**: The current system already achieves excellent separation:
- **Voice Processing**: Follows Check-In pattern with immediate history accumulation
- **Response Processing**: Agent processes full conversation history context
- **State Management**: AgentCortex provides single source of truth
- **Integration**: Clean ViewModel observation without complex routing

### 2.3. PRESERVATION: Working Integration Patterns Must Be Maintained

**CRITICAL SUCCESS FACTORS**: The extraction must preserve these working integration patterns:

1. **Core Loop Phase Routing**: `launchConceptScreenForPhase()` seamlessly integrates concept building with Core Loop phases
2. **Voice Input Processing**: `handleUserResponse()` → `addToHistory()` pattern that follows Check-In system
3. **Response Button Processing**: Agent processing of accumulated conversation history
4. **State Synchronization**: Current system maintains perfect sync between agent state and concept screen state
5. **Navigation Coordination**: Complex navigation patterns that preserve conversation context
6. **Error Recovery**: Existing error handling that maintains conversation continuity

## 3. ALTERNATIVE APPROACH: Incremental Modularization

### 3.1. RECOMMENDATION: Follow Existing Modularization Patterns

**BETTER APPROACH**: Instead of the complex "Delegated Control & Routing" model, follow the successful patterns already established in the codebase:

**Existing Successful Patterns:**
- **commands/**: Function-based command processing with clean separation
- **coreloop/**: CoreLoopManager follows "frontal cortex" pattern - processes internally, returns results
- **utilities/**: Consolidated utility functions in focused modules
- **components/**: Decomposed UI components with specific responsibilities

### 3.2. PROPOSED: ConceptManager as Processing Engine

**ALIGNED APPROACH**: Follow the CoreLoopManager pattern:

```kotlin
class ConceptManager(
    private val conceptRepository: ConceptRepository,
    private val brainService: BrainService
) {
    // Processing engine - NO external interface access
    suspend fun processConceptAction(
        context: ConceptBuildingContext,
        userInput: String?
    ): ConceptProcessingResult

    // Internal processing only - returns structured results (renamed for clarity)
    private suspend fun generateConceptResponse(context: ConceptBuildingContext): BrainDecision
    private fun createConceptToolLibrary(): ConceptToolLibrary
    private fun buildConceptPrompt(context: ConceptBuildingContext): String
}

data class ConceptProcessingResult(
    val speechResponse: String,
    val action: ConceptAction,
    val updatedContext: ConceptBuildingContext?,
    val shouldExit: Boolean = false
)
```

**Benefits:**
- **Follows Established Patterns**: Matches CoreLoopManager architecture
- **Preserves Working Integration**: Maintains existing ConversationAgent orchestration
- **Reduces Complexity**: No new communication channels or routing logic
- **Maintains State Control**: ConversationAgent retains control of external interfaces

### 3.3. INTEGRATION: Preserve Existing Communication Patterns

**CRITICAL**: Maintain the working patterns that already exist and follow Check-In system model:

1. **ConversationAgent.observeConceptViewModel()**: Keep this integration point
2. **ConceptViewModel.getConceptBuildingContext()**: Preserve this context bridge
3. **Voice Input Processing**: Maintain `handleUserResponse()` → `addToHistory()` pattern
4. **Response Button Processing**: Keep agent processing of accumulated conversation history
5. **AgentCortex UiIntent System**: Use existing `UiIntent.SendResponse` and `UiIntent.NotifyConceptScreenExit`
6. **Direct ViewModel Access**: Maintain `currentConceptViewModel` for state synchronization
7. **Conversation History Integration**: Enhance `ConceptBuildingContext` to include conversation history for LLM context

## 4. CORRECTED Program Flow: Incremental Extraction

### 4.1. CURRENT Working Flow (To Be Preserved)

```
1. Core Loop Phase Transition:
   ConversationAgent.launchConceptScreenForPhase()
   → Sets ConversationType.ConceptBuilding
   → Navigates to ConceptScreen with wish ID
   → ConceptViewModel initializes and loads data

2. Agent-ViewModel Integration:
   ConversationAgent.observeConceptViewModel()
   → Monitors DataLoadingState.Ready
   → Calls initiateConceptBuilding() when ready
   → Establishes bidirectional communication

3. Concept Building Loop:
   ConversationAgent.getNextBrainDecision()
   → Gets ConceptBuildingContext from ViewModel
   → Calls BrainService.getNextConceptAction()
   → Processes tools and speaks responses
   → Continues until completion

4. Exit and Return:
   ConceptViewModel.onCleared() → UiIntent.NotifyConceptScreenExit
   → ConversationAgent.onConceptScreenExit()
   → Navigates back to MainScreen
   → Continues Core Loop progression
```

**ANALYSIS**: This flow already works perfectly and maintains all necessary state continuity.

### 4.2. PROPOSED Incremental Extraction Flow

**SAFER APPROACH**: Extract processing logic while preserving integration patterns:

```
1. Create ConceptManager as Processing Engine:
   ConceptManager.processConceptAction(context, userInput)
   → Internal processing (BrainService calls, tool execution)
   → Returns ConceptProcessingResult with speech and actions
   → NO external interface access (no TTS, no navigation)

2. Update ConversationAgent Integration:
   ConversationAgent.getNextBrainDecision()
   → Gets context from ConceptViewModel (unchanged)
   → Calls conceptManager.processConceptAction() (NEW)
   → Processes returned result (speaks, updates state, etc.)
   → Maintains all existing orchestration logic

3. Preserve All Existing Patterns:
   → observeConceptViewModel() integration (unchanged)
   → UiIntent communication system (unchanged)
   → Core Loop phase routing (unchanged)
   → Navigation and state management (unchanged)
```

**BENEFITS**:
- Extracts complex processing logic
- Maintains working integration patterns
- Reduces risk of breaking existing functionality
- Follows established architectural patterns

## 5. REVISED Key Data Structures & Files

### 5.1. New Files (Minimal Changes)
*   **New File:** `app/src/main/java/com/example/voxmanifestorapp/ui/agent/concept/ConceptManager.kt`
*   **Modified File:** `app/src/main/java/com/example/voxmanifestorapp/data/AgentClasses.kt` to add:

```kotlin
data class ConceptProcessingResult(
    val speechResponse: String,
    val shouldContinue: Boolean,
    val updatedContext: ConceptBuildingContext?,
    val completionAction: String? = null // For phase completion
)
```

### 5.2. Files to Modify (Minimal Impact)
- **ConversationAgent.kt**: Extract `getNextBrainDecision()` logic to ConceptManager, maintain orchestration
- **AppContainer.kt**: Add ConceptManager dependency injection
- **AppViewModelProvider.kt**: NO CHANGES (ConceptManager injected into ConversationAgent, not ConceptViewModel)

## 6. REVISED Acceptance Criteria (Risk-Reduced Approach)

### 6.1. Phase 1: Core Extraction (Low Risk)
- [ ] Create `ConceptManager.kt` in `ui/agent/concept/` package following CoreLoopManager pattern
- [ ] Extract `getNextBrainDecision()` processing logic to `ConceptManager.processConceptAction()`
- [ ] Add `ConceptProcessingResult` data class to `AgentClasses.kt`
- [ ] Add ConceptManager to `AppContainer.kt` dependency injection
- [ ] Update `ConversationAgent.getNextBrainDecision()` to delegate to ConceptManager
- [ ] Preserve ALL existing integration patterns (observeConceptViewModel, UiIntent system, etc.)

### 6.2. Phase 2: Validation (Critical)
- [ ] Verify Core Loop → Concept Screen → Core Loop flow works identically
- [ ] Test concept building conversation maintains all existing functionality
- [ ] Confirm navigation and state management unchanged
- [ ] Validate error handling and conversation scope management
- [ ] Test screen exit and cleanup processes

### 6.3. Success Criteria
- [ ] ConversationAgent reduced in size while maintaining orchestration role
- [ ] Concept processing logic isolated in dedicated manager
- [ ] Zero functional regressions in concept building experience
- [ ] All existing integration patterns preserved and working

## 7. CRITICAL Risk Analysis & Mitigation

### 7.1. HIGH RISK: Breaking Working Integration
**Risk:** The original PRD's "Delegated Control & Routing" approach risks breaking the sophisticated integration between ConversationAgent, ConceptViewModel, and Core Loop phases.

**Mitigation:**
- Follow incremental extraction approach
- Preserve existing communication patterns
- Maintain ConversationAgent orchestration role
- Test extensively at each step

### 7.2. MEDIUM RISK: State Synchronization Issues
**Risk:** Extracting concept logic might disrupt the careful state synchronization between agent and concept screen.

**Mitigation:**
- Keep ConversationAgent as state orchestrator
- Maintain existing AgentCortex integration
- Preserve ConceptViewModel observation patterns
- Test state transitions thoroughly

### 7.3. LOW RISK: Dependency Injection Complexity
**Risk:** Adding ConceptManager to dependency injection might create circular dependencies.

**Mitigation:**
- Inject ConceptManager into ConversationAgent (not ConceptViewModel)
- Follow existing patterns from CoreLoopManager injection
- Keep dependency graph simple and unidirectional

## 8. IMPLEMENTATION STRATEGY: Incremental and Safe

### 8.1. Step 1: Create ConceptManager Shell
```kotlin
class ConceptManager(
    private val conceptRepository: ConceptRepository,
    private val brainService: BrainService
) {
    suspend fun processConceptAction(
        context: ConceptBuildingContext,
        userInput: String? = null
    ): ConceptProcessingResult {
        // Move getNextBrainDecision logic here (renamed to generateConceptResponse)
        // Include conversation history from enhanced context
        // Return structured result instead of direct TTS calls
    }
}
```

### 8.2. Step 2: Update ConversationAgent Integration
```kotlin
// In ConversationAgent - rename getNextBrainDecision to processConceptInteraction
private suspend fun processConceptInteraction(
    context: ConceptBuildingContext,
    userInput: String? = null
) {
    val result = conceptManager.processConceptAction(context, userInput)

    // Agent handles all external interfaces
    speak(result.speechResponse)
    addToHistory(Speaker.Agent, result.speechResponse, agentCortex.coreLoopState.value.currentPhase)

    if (result.shouldContinue) {
        changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
    } else {
        handleConceptPhaseCompletion(result)
    }
}
```

### 8.3. Step 3: Preserve All Existing Patterns
- Keep `observeConceptViewModel()` unchanged
- Maintain `UiIntent` communication system
- Preserve voice input processing: `handleUserResponse()` → `addToHistory()` pattern
- Keep response button processing of accumulated conversation history
- Preserve Core Loop integration
- Keep navigation and state management in ConversationAgent
- Enhance `ConceptBuildingContext` to include conversation history for better LLM context

## 9. CONCLUSION: Safer Path Forward

**RECOMMENDATION**: Proceed with incremental extraction that respects the working Check-In pattern:

1. **Follows Established Patterns**: Uses CoreLoopManager as the architectural template
2. **Preserves Working Integration**: Maintains all existing communication and state patterns including Check-In style voice processing
3. **Respects Current Architecture**: Acknowledges the sophisticated voice input and response processing already in place
4. **Reduces Risk**: Minimizes changes to working systems that already follow best practices
5. **Achieves Goals**: Extracts concept processing logic while maintaining orchestration patterns
6. **Enables Testing**: Allows thorough validation at each step
7. **Enhances Context**: Improves LLM context by including conversation history in ConceptBuildingContext

This approach respects the sophisticated architecture already in place, acknowledges that the current system already follows the Check-In pattern correctly, and achieves the desired modularization goals safely and incrementally.