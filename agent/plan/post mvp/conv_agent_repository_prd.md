# ConversationAgent Refactoring: Repository Pattern PRD

## 1. Overview

This document outlines the analysis and implementation plan for refactoring the monolithic `ConversationAgent` by introducing a `ConversationRepository`. The goal is to align the conversation management system with modern Android architecture best practices, improving testability, maintainability, and scalability while resolving critical lifecycle and state management issues.

This plan is based on a detailed analysis of the existing architecture as documented in `convo_agent_context.md`, `checkin_context.md`, and `conversation_states_and_ui.md`.

## 2. Problem Statement & Current State Analysis

The `ConversationAgent` currently acts as a "God Object," a central orchestrator managing numerous responsibilities that should be separated across different architectural layers. This creates several critical issues:

*   **Mixed Responsibilities:** The agent intertwines UI coordination (speech, navigation), business logic (Core Loop progression, Check-In flow), and lifecycle management (CoroutineScope ownership). This violates the Single Responsibility Principle.
*   **Lifecycle & Navigation Issues:** As identified in the initial analysis, tying the `conversationScope` to the `ConversationAgent` (a UI-layer component) creates a fragile system where navigation events can improperly terminate or replace long-running conversation processes. This is the root cause of the `ConceptBuilding` scope replacing the `CoreLoop` scope.
*   **Poor Testability:** The monolithic nature of the agent makes it nearly impossible to unit test individual pieces of business logic (e.g., `progressCoreLoop`) without mocking a vast and complex set of dependencies (UI, state, voice, etc.).
*   **High Maintenance Overhead:** At over 2,400 lines, the `ConversationAgent` is difficult to navigate, debug, and safely modify.

As detailed in `convo_agent_context.md`, the agent is intended to be the "frontal cortex"—an orchestrator, not the engine. The current implementation conflates these two roles.

## 3. Proposed Architecture: The Repository Pattern

To resolve these issues, we will introduce a `ConversationRepository` that owns the conversation's business logic and lifecycle, allowing the `ConversationAgent` to focus solely on UI/IO coordination.

### 3.1. Architecture Overview

#### High-Level Architecture Comparison

```mermaid
flowchart TB
    subgraph Before ["🔴 Before: Monolithic"]
        UI1[UI Layer] --> Agent1[ConversationAgent<br/>God Object]
        Agent1 --> Services1[All Services]
        Agent1 --> State1[State Management]
        Agent1 --> Logic1[Business Logic]
    end
    
    subgraph After ["🟢 After: Repository Pattern"]
        UI2[UI Layer] --> Agent2[ConversationAgent<br/>Coordinator]
        Agent2 <--> Repo[ConversationRepository<br/>Engine]
        Agent2 --> Services2[UI/IO Services]
        Repo --> Logic2[Business Logic]
        Repo --> State2[State Management]
    end
    
    Before -.->|Refactor| After
    
    style Before fill:#ffe6e6
    style After fill:#e6ffe6
    style Agent1 fill:#ffcccc
    style Agent2 fill:#cce6ff
    style Repo fill:#ccffcc
```

### 3.2. Visualizing the Architectural Shift

#### Diagram 1: Current Monolithic Flow

```mermaid
sequenceDiagram
    participant UI as 🖥️ UI (Screen)
    participant ConvAgent as 🧠 ConversationAgent<br/>(God Object)
    participant Cortex as 🎯 AgentCortex<br/>(State)
    participant Voice as 🔊 VoiceService<br/>(TTS/STT)
    participant Brain as 🤖 BrainService<br/>(LLM)
    participant Nav as 🗺️ NavigationManager

    UI->>ConvAgent: 1. User input (voice/tap)
    
    rect rgb(255, 200, 200)
        Note over ConvAgent: PROBLEM: Owns everything
        ConvAgent->>Cortex: 2. Update state (e.g., Thinking)
        ConvAgent->>Brain: 3. Process logic & call LLM
        Brain-->>ConvAgent: 4. LLM Response
        ConvAgent->>Voice: 5a. Speak response
        ConvAgent->>Cortex: 5b. Update history & state
        ConvAgent->>Nav: 5c. (Sometimes) Navigate to new screen
    end
    
    Note right of ConvAgent: ❌ Mixed Responsibilities:<br/>UI + Business Logic + Lifecycle
```

#### Diagram 2: Proposed Decoupled Flow

```mermaid
sequenceDiagram
    participant UI as 🖥️ UI (Screen)
    participant ConvAgent as 🎭 ConversationAgent<br/>(Coordinator)
    participant ConvRepo as ⚙️ ConversationRepository<br/>(Engine)
    participant Cortex as 🎯 AgentCortex<br/>(State)
    participant Voice as 🔊 VoiceService<br/>(TTS/STT)
    participant Brain as 🤖 BrainService<br/>(LLM)
    participant Nav as 🗺️ NavigationManager

    UI->>ConvAgent: 1. User input (voice/tap)
    ConvAgent->>ConvRepo: 2. Delegate input ("process this")

    rect rgb(200, 255, 200)
        Note over ConvRepo: SOLUTION: Owns business logic
        ConvRepo->>Brain: 3. Process logic & call LLM
        Brain-->>ConvRepo: 4. LLM Response
        
        loop Event Observation
            ConvRepo-->>ConvAgent: 5. Emit Event
        end
    end
    
    rect rgb(200, 200, 255)
        Note over ConvAgent: SOLUTION: Pure coordinator
        alt SpeakText Event
            ConvAgent->>Voice: 6a. Speak text
        else NavigateTo Event
            ConvAgent->>Nav: 6b. Navigate to screen
        else UpdateState Event
            ConvAgent->>Cortex: 6c. Update state
        end
    end
    
    Note right of ConvAgent: ✅ Clear Separation:<br/>Repository = Logic<br/>Agent = UI/IO
```

### 3.2. Responsibility Split: Before vs. After

```mermaid
graph TD
    A[ConversationAgent Current<br/>2400 lines] --> B[CoroutineScope Management]
    A --> C[Business Logic]
    A --> D[LLM Service Calls]
    A --> E[State Updates]
    A --> F[UI/IO Actions]
    
    G[ConversationRepository New] --> H[CoroutineScope Management]
    G --> I[Business Logic]
    G --> J[LLM Service Calls]
    G --> K[Emit Events]
    
    L[ConversationAgent New<br/>1200 lines] --> M[Delegate to Repository]
    L --> N[Listen for Events]
    L --> O[Update State]
    L --> P[UI/IO Actions]
    
    K --> N
    
    style A fill:#ffcccc
    style G fill:#ccffcc
    style L fill:#cce6ff
```

#### Detailed Responsibility Matrix

| Responsibility | Current (`ConversationAgent`) | Proposed (`ConversationRepository`) | Proposed (`ConversationAgent`) |
| :--- | :--- | :--- | :--- |
| **Conversation Lifecycle (Scope)** | ✅ Owns and manages `CoroutineScope` | ✅ **Owns and manages `CoroutineScope`** | ❌ Delegates to Repository |
| **Business Logic (Core Loop, etc.)** | ✅ Contains all logic | ✅ **Contains all logic** | ❌ Delegates to Repository |
| **LLM Service Calls** | ✅ Calls `BrainService` directly | ✅ **Calls `BrainService` directly** | ❌ No direct calls |
| **State Updates (AgentCortex)** | ✅ Pushes all state updates | ➡️ Emits state change *events* | ✅ **Observes events & updates Cortex** |
| **UI/IO Actions (Speak, Navigate)** | ✅ Calls services directly | ❌ **No UI/IO access** | ✅ **Calls services based on events** |


## 4. Phased Implementation Plan

```mermaid
graph LR
    A[Phase 1: Foundation] --> B[Phase 2: Core Logic]
    B --> C[Phase 3: Check-In]
    C --> D[Phase 4: Cleanup]
    
    A --> A1[Create Repository]
    A --> A2[Define Models]
    A --> A3[Setup DI]
    A --> A4[Move Scope]
    
    B --> B1[Migrate Core Loop]
    B --> B2[Adapt to Events]
    B --> B3[Update Agent]
    
    C --> C1[Migrate Check-In]
    C --> C2[Migrate Concept Building]
    
    D --> D1[Remove Dead Code]
    D --> D2[Verify Agent Role]
    D --> D3[Update Documentation]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

This is a significant refactoring. It will be executed in phases to minimize risk.

### Phase 1: Create the `ConversationRepository`
1.  **Create File:** Create `data/ConversationRepository.kt`.
2.  **Define Models:** Implement the initial `ConversationState` and `ConversationEvent` sealed classes.
3.  **Setup DI:** Add the new repository to `AppContainer.kt` and inject it into `ConversationAgent`.
4.  **Move Scope:** Move the `conversationScope` and its basic lifecycle management (`start`, `stop`) from the agent to the repository.

### Phase 2: Migrate Core Loop Logic
1.  **Migrate Functions:** Move `progressCoreLoop` and its helper functions from the agent to the repository.
2.  **Adapt Logic:** Refactor the migrated functions to emit `ConversationEvent`s instead of directly calling `speak()`, `updateCoreLoopState()`, etc.
3.  **Update Agent:** The `ConversationAgent` will now call `conversationRepository.startConversation(CoreLoop)` and observe the resulting events.

### Phase 3: Migrate Check-In & Concept Building Logic
1.  **Migrate Check-In:** Move the `progressCheckIn` orchestration logic to the repository. The repository will call `DialogueChain` and then emit events based on the outcome.
2.  **Migrate Concept Building:** Move `initiateConceptBuilding` and `getNextBrainDecision` to the repository. The repository will now handle the transition from `CoreLoop` to `ConceptBuilding` internally and emit the appropriate `NavigateTo` event.

### Phase 4: Final Cleanup
1.  **Remove Dead Code:** Delete all migrated functions from `ConversationAgent`.
2.  **Verify Agent's Role:** Ensure the `ConversationAgent` is now purely a UI/IO coordinator.
3.  **Update Documentation:** Update `convo_agent_context.md` and other relevant documents to reflect the new architecture.

## 5. Success Criteria

```mermaid
flowchart LR
    subgraph Metrics ["📊 Quantitative Metrics"]
        Size["📏 File Size<br/>-50% reduction<br/>(2400→1200 lines)"]
        Logic["🧠 Business Logic<br/>100% in Repository"]
        Calls["🔗 Direct LLM Calls<br/>0 in Agent"]
    end
    
    subgraph Functional ["⚙️ Functional Criteria"]
        Navigation["🗺️ Check-In → Concept<br/>Seamless transition"]
        State["💾 State Preservation<br/>No conversation loss"]
        Features["🎯 Feature Parity<br/>All existing functionality"]
    end
    
    subgraph Quality ["🔬 Quality Improvements"]
        Tests["🧪 Unit Testability<br/>Repository methods"]
        Separation["🔀 Concern Separation<br/>Clear responsibilities"]
        Maintainability["🛠️ Maintainability<br/>Easier to modify"]
    end
    
    Metrics --> Success["✅ Refactoring Success"]
    Functional --> Success
    Quality --> Success
    
    style Success fill:#90EE90
```

### Detailed Success Criteria

*   **📏 File Size Reduction**: The `ConversationAgent.kt` file size is reduced by at least 50% (from ~2400 to ~1200 lines).
*   **🧠 Business Logic Migration**: All conversation business logic and `CoroutineScope` management is located within `ConversationRepository`.
*   **🔗 Service Decoupling**: The `ConversationAgent` no longer directly calls `BrainService` for conversation progression.
*   **🗺️ Navigation Preservation**: The application successfully navigates from the Check-In phase to the Concept Screen, driven by the new architecture, without losing conversation state.
*   **🎯 Feature Parity**: All existing Core Loop and Check-In functionality is preserved.
*   **🧪 Testability**: The system is more testable, demonstrated by the ability to write unit tests for the public methods in `ConversationRepository`.