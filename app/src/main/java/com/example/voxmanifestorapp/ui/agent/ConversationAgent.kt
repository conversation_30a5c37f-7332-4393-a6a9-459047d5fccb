package com.example.voxmanifestorapp.ui.agent

import ManifestationRepository
import android.util.Log
import com.example.voxmanifestorapp.data.ConceptActionState
import com.example.voxmanifestorapp.data.ConceptBuildingContext
import com.example.voxmanifestorapp.data.ConceptRepository
import com.example.voxmanifestorapp.data.ConceptType
import com.example.voxmanifestorapp.data.ConversationRepository
import com.example.voxmanifestorapp.data.DataLoadingState
import com.example.voxmanifestorapp.data.Manifestation
import com.example.voxmanifestorapp.data.StatusColor
import com.example.voxmanifestorapp.ui.agent.affirmations.AffirmationManager
import com.example.voxmanifestorapp.ui.agent.checkin.CheckInDialogueChain
import com.example.voxmanifestorapp.ui.agent.checkin.CheckInStage
import com.example.voxmanifestorapp.ui.agent.checkin.ConversationalTheme
import com.example.voxmanifestorapp.ui.agent.checkin.Response
import com.example.voxmanifestorapp.ui.agent.checkin.TransitionActionPlan
import com.example.voxmanifestorapp.ui.agent.checkin.TransitionChain
import com.example.voxmanifestorapp.ui.agent.commands.*
import com.example.voxmanifestorapp.ui.agent.coreloop.CoreLoopManager
import com.example.voxmanifestorapp.ui.agent.navigation.NavigationManager
import com.example.voxmanifestorapp.ui.agent.timer.CheckInTimerManager
import com.example.voxmanifestorapp.ui.agent.timer.TimerIntent
import com.example.voxmanifestorapp.ui.agent.utilities.fetchEnhancedWishDataForTransition
import com.example.voxmanifestorapp.ui.agent.utilities.fetchUserWishesForContext
import com.example.voxmanifestorapp.ui.agent.utilities.handleError
import com.example.voxmanifestorapp.ui.agent.voice.*
import com.example.voxmanifestorapp.ui.basevox.TextToSpeechRepository
import com.example.voxmanifestorapp.ui.basevox.VoiceManagedViewModel
import com.example.voxmanifestorapp.ui.concept.ConceptViewModel
import com.example.voxmanifestorapp.ui.main.MainScreenState
import com.example.voxmanifestorapp.ui.statuslog.StatusMessageService
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.cancellation.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonPrimitive

/*  This is our Conversational Agent, the "conversation brain" of the app
    - control recog system via VmVm; when to start & stop listening
    - receives raw text from VmVm
    - process that into commands or conversation responses
    - issue conversational prompts, conversational flow management, goal-seeking behaviour
    - decide when to speak and listen
*/

class ConversationAgent(
        private val repository: ManifestationRepository,
        private val ttsRepo: TextToSpeechRepository,
        private val brainService: BrainService,
        private val scope: CoroutineScope,
        private val statusService: StatusMessageService,
        private val conceptRepository: ConceptRepository,
        private val mainScreenState: MainScreenState,
        private val agentCortex: AgentCortex,
        private val conversationRepository: ConversationRepository
) {
  private val TAG = "genie"

  // Add flag to track first response
  private var isFirstResponse = true

  // control voice input and output (Text to Speech and Speech to Text)
  private var voiceManager: VoiceManagedViewModel? = null

  /** Agent Cortex plug-in : states that will change based on user interactions */
  val dialogueState =
          agentCortex.dialogueState // manage back and forth dialogue states between genie and user
  val currentDisplayState = agentCortex.displayState // Display state for affirmations UI
  val conversationHistory =
          agentCortex.conversationHistory // conversation history for brain conversations in concept
  // screen
  val conversationPlan = agentCortex.conversationPlan // receive brain's plan for the conversation
  val currentAction =
          agentCortex.currentAction // track where the brain is at with its conversations
  val checkInState = agentCortex.checkInState // state info for the check-in stage of the core loop.

  // get the current slot index from the main screen's API for interacting with wishes
  private val currentSlotIndex = mainScreenState.selectedSlot

  val navigationManager = NavigationManager() // Navigation management

  private val checkInTimerManager =
          CheckInTimerManager(
                  // check in timer manager for timing check in
                  agentCortex = agentCortex,
                  coroutineScope = scope,
          )

  val affirmationManager =
          AffirmationManager( // offload affirmations functions to affirmation mgr.
                  brainService = brainService,
                  agentCortex = agentCortex,
                  logger = ::logStatus,
                  scope = scope
          )

  private val voiceProcessor =
          VoiceProcessor(
                  ttsRepo = ttsRepo,
                  agentCortex = agentCortex,
                  logger = ::logStatus,
                  scope = scope
          )

  private val commandMode =
          CommandMode(
                  repository = repository,
                  agentCortex = agentCortex,
                  mainScreenState = mainScreenState,
                  logger = ::logStatus,
                  voiceProcessor = voiceProcessor,
                  navigationManager = navigationManager
          )

  /** agent's working memory array (internal state management) */
  // provides the most recently spoken command, as filtered through the voice command entity
  // commandState

  /** BRAIN: conversation management variables */
  // most important conversation management variable: scope variable allows cancelling existing
  // conversation at any point
  private var conversationScope: CoroutineScope? = null

  // Use AgentCortex as single source of truth for conversation type
  private val currentConversation: ConversationType?
    get() = agentCortex.conversationType.value

  // Legacy state variables removed - now handled by CommandMode

  /*      listeningjob deprecated for now in favour of manual button press.
  // manage pauses in the user's responses
  private var lastUtteranceTime = MutableStateFlow<Long>(0L)
  private var listeningJob: Job? = null
  private val PAUSE_THRESHOLD = 10000L         // 10 seconds pause
   */

  // track which screen the app is on
  val currentScreen = MutableStateFlow<Screen>(Screen.Main)

  enum class Screen {
    Main,
    Concept
  }

  // agent needs access to concept view model so it can control the visual interface
  private var currentConceptViewModel: ConceptViewModel? = null

  // set up the flow of manifestations from the repository into this viewmodel
  val allManifestations: Flow<List<Manifestation>> = repository.getAllManifestations()

  // the data type of the argument navigate is a lambda function receiving the manifestation id and
  // producing the navigation function
  fun setConceptNavigation(navigate: (Int) -> Unit) {
    navigationManager.setConceptNavigation(navigate)
  }

  // todo: integrate this tool with the other brain tools at some point?
  /* agent brain variables */

  // flag to prevent multiple conversation toggles at once
  private val isToggleInProgress = AtomicBoolean(false)

  // DialogueChain for check-in conversation management
  private val checkInDialogueChain =
          CheckInDialogueChain(
                  brainService = brainService,
                  agentCortex = agentCortex,
                  logger = { msg, color -> logStatus(msg, color) }
          )

  // TransitionChain for generating intelligent transition suggestions
  private val transitionChain =
          TransitionChain(
                  brainService = brainService,
                  commandMode = commandMode,
                  logger = { msg, color -> logStatus(msg, color) }
          )

  // logger
  private fun logStatus(message: String, color: StatusColor = StatusColor.Default) {
    Log.d(TAG, message)
    statusService.addStatusMessage("[" + TAG + "] " + message, color)
  }

  /**
   * Adds a conversation entry to both in-memory history and persistent storage. Now includes
   * metadata support for theme persistence.
   */
  private fun addToHistory(
          speaker: Speaker,
          content: String,
          phase: ConversationPhase,
          isVisible: Boolean = true,
          metadata: Map<String, JsonElement> = emptyMap()
  ) {
    // Check if this is a duplicate user message
    if (speaker == Speaker.User) {
      // Get the last few entries (enough to cover potential duplicates)
      val recentEntries = agentCortex.conversationHistory.value.takeLast(5)

      // Check if any recent entry from the user has identical content
      val isDuplicate =
              recentEntries.any { entry ->
                entry.speaker == Speaker.User && entry.content == content
              }

      // Skip adding if it's a duplicate
      if (isDuplicate) {
        logStatus(
                "Skipping duplicate user message: ${content.take(20)}${if (content.length > 20) "..." else ""}",
                StatusColor.Default
        )
        return
      }
    }

    // Special tracking for agent responses
    if (speaker == Speaker.Agent) {
      // --- ENHANCED LOGGING: Agent response metadata tracking ---
      val sessionId = conversationRepository.getCurrentSessionId()
      Log.d("AddToHistory", "=== AGENT RESPONSE METADATA TRACKING ===")
      Log.d("AddToHistory", "SessionId: $sessionId, Phase: $phase, Speaker: $speaker")
      Log.d("AddToHistory", "Content length: ${content.length} chars")
      Log.d("AddToHistory", "Metadata keys: ${metadata.keys.joinToString(", ")}")
      Log.d("AddToHistory", "Full metadata: $metadata")

      Log.d("AddToHistory", "🔍 AGENT RESPONSE DETECTED - ANALYZING METADATA:")
      Log.d("AddToHistory", "  - Has themes: ${metadata.containsKey("themes")}")
      Log.d("AddToHistory", "  - Has strategy: ${metadata.containsKey("strategy")}")
      Log.d("AddToHistory", "  - Has reasoning: ${metadata.containsKey("reasoning")}")
      Log.d("AddToHistory", "  - Has sessionContext: ${metadata.containsKey("sessionContext")}")

      metadata["themes"]?.let { themesJsonElement ->
        Log.d("AddToHistory", "🎯 THEMES FOUND IN AGENT RESPONSE:")
        Log.d("AddToHistory", "  - Themes JsonElement: $themesJsonElement")
        try {
          val json = Json {
            ignoreUnknownKeys = true
            isLenient = true
            coerceInputValues = true
          }
          val themes = json.decodeFromJsonElement(kotlinx.serialization.serializer<List<ConversationalTheme>>(), themesJsonElement)
          Log.d("AddToHistory", "  - Successfully parsed ${themes.size} themes:")
          themes.forEachIndexed { index, theme ->
            Log.d(
                    "AddToHistory",
                    "    ${index + 1}. '${theme.title}' (${theme.observations.size} observations)"
            )
            theme.observations.take(3).forEach { observation ->
              Log.d("AddToHistory", "      - ${observation.take(50)}...")
            }
          }
        } catch (e: Exception) {
          Log.w("AddToHistory", "❌ Failed to parse themes JsonElement: ${e.message}")
          Log.w("AddToHistory", "❌ Raw themes JsonElement: $themesJsonElement")
        }
      }
              ?: Log.d("AddToHistory", "❌ NO THEMES FOUND IN AGENT RESPONSE METADATA")
    }
    Log.d("AddToHistory", "=== END AGENT RESPONSE METADATA TRACKING ===")
    // --- END ENHANCED LOGGING ---

    // Create in-memory conversation entry for UI/current session
    // Convert JsonElement metadata to String metadata for ConversationEntry
    val stringMetadata = metadata.mapValues { (_, jsonElement) ->
        when (jsonElement) {
            is JsonPrimitive -> jsonElement.content
            else -> jsonElement.toString() // For JsonArray, JsonObject, etc.
        }
    }

    val newEntry =
            ConversationEntry(
                    speaker = speaker,
                    content = content,
                    phase = phase,
                    isVisible = isVisible,
                    metadata = stringMetadata
            )

    // Add to in-memory history via AgentCortex
    agentCortex.addConversationEntry(newEntry)

    // Log to persistent storage if available
    // Using a non-blocking launch to avoid delays in the UI
    scope.launch {
      try {
        // Get current wishId from coreLoopState. Phase is now passed in.
        val currentWishId = agentCortex.coreLoopState.value.currentWishIndex

        // Log to repository with metadata
        conversationRepository.logConversation(
                speaker = speaker,
                content = content,
                wishId = currentWishId,
                phase = phase,
                isVisible = isVisible,
                metadata = metadata // ← PASS METADATA TO DATABASE
        )
      } catch (e: Exception) {
        logStatus("Failed to log conversation: ${e.message}", StatusColor.Stop)
      }
    }
  }

  init {
    Log.d("AgentInit", "New ConversationAgent instance created with hash: ${hashCode()}")

    val agentId = System.identityHashCode(this)
    Log.d("AgentDebug", "Agent instance created with ID: $agentId")

    // Setup monitoring of UI state changes
    monitorUiIntents()

    // Monitor slot selection from MainScreenState
    monitorSlotSelection()
  }

  private fun monitorUiIntents() {
    // Monitor interrupt requests
    /**
     * monitor interrupt request so we can halt agent's speech instantly this is a stop-gap solution
     * instead of being able to actually interrupt the agent with speech, which will be a later
     * feature
     */
    scope.launch {
      agentCortex.uiIntentFlow.collect { intent ->
        Log.d(TAG, "Agent received UI Intent: ${intent::class.simpleName}")
        when (intent) {
          is AgentCortex.UiIntent.RequestInterrupt -> {
            logStatus("UI Intent: Interrupt speech requested", StatusColor.Pause)
            interruptSpeech()
            resetConversation()
          }
          is AgentCortex.UiIntent.RequestToggleConversation -> {
            logStatus(
                    "UI Intent: Toggle conversation requested (current state: ${currentConversation})",
                    StatusColor.Pause
            )
            toggleBrainConversation()
          }
          is AgentCortex.UiIntent.RequestTerminateConversation -> {
            logStatus("UI Intent: Terminate conversation requested", StatusColor.Pause)
            terminateConversation()
          }
          is AgentCortex.UiIntent.SendResponse -> {
            logStatus("UI Intent: Send response requested", StatusColor.Go)
            conversationScope?.launch { processUserResponse() }
          }
          is AgentCortex.UiIntent.NotifyConceptScreenExit -> {
            logStatus("UI Intent: Concept screen exit notification received", StatusColor.Pause)
            scope.launch { // Use launch if onConceptScreenExit is suspend
              onConceptScreenExit()
            }
          }
          is AgentCortex.UiIntent.InitiateCoreLoop -> {
            logStatus("UI Intent: Core Loop initiation requested", StatusColor.Go)
            scope.launch { handleCoreLoopIntent(intent.startPhase) }
          }
          is AgentCortex.UiIntent.TimerControl -> {
            logStatus("UI Intent: Timer control requested", StatusColor.Go)
            conversationScope?.launch { handleTimerControl(intent.timerIntent) }
          }
        }
      }
    }
  }

  private suspend fun handleTimerControl(intent: TimerIntent) {
    Log.d(TAG, "Handling timer control: $intent")
    when (intent) {
      TimerIntent.AddTime -> {
        Log.d(TAG, "Adding time to timer")
        checkInTimerManager.addTime()
      }
      TimerIntent.SubtractTime -> {
        Log.d(TAG, "Subtracting time from timer")
        checkInTimerManager.subtractTime()
      }
      TimerIntent.Stop -> {
        Log.d(TAG, "Stopping timer")
        checkInTimerManager.forceTransition()
        progressCheckIn()
      }
    }
  }

  /** sets up a collection to run when a slot is selected, either through UI or voice. */
  private fun monitorSlotSelection() {
    scope.launch {
      var previousSlot = -1
      mainScreenState.selectedSlot.collect { newSlot ->
        // Update logic (same as the original function)
        Log.d("SLOT", "Agent - selected slot = ${newSlot}")

        // Only process selection changes, not deselections
        if (newSlot >= 0 && newSlot != previousSlot) {
          // Check microphone status
          val micActive = isMicrophoneActive(voiceManager)

          if (!micActive) {
            logStatus(
                    "Microphone must be enabled to perform operations on wishes",
                    StatusColor.Pause
            )
          } else if (currentConversation == null) {
            // Process the manual selection only if mic is active and we're not in a conversation
            commandMode.selectWishBySlot(newSlot)
            updateConversationState(ConversationType.CommandMode)
          }
        }
        previousSlot = newSlot
      }
    }
  }

  // called from Concept Screen when mounted so agent knows its there and has access to view model
  /**
   * todo: currently called from NavHost directly. May need to be mediated by agent cortex in future
   * Observes the loading state of the concept view model to determine when
   * ```
   *      the concept screen is ready for interaction.
   * ```
   * Also sets up collections from the concept screen state API to maintain awareness of user
   * changes in UI on concept screen
   */
  fun observeConceptViewModel(viewModel: ConceptViewModel) {
    logStatus("Starting to observe ConceptViewModel loading state")

    currentConceptViewModel = viewModel

    val agentId = System.identityHashCode(this)
    val viewModelId = System.identityHashCode(viewModel)
    Log.d("AgentDebug", "Agent $agentId observing ConceptViewModel $viewModelId")

    /**
     * monitors state of data loading in concept screen so brain conversation can be auto-initiated
     */
    scope.launch {
      viewModel.dataLoadingState.collect { state ->
        when (state) {
          DataLoadingState.Ready -> {
            logStatus("ConceptViewModel data is ready", StatusColor.Go)

            // Get the current context
            currentScreen.value = Screen.Concept

            // If we were directed to build concepts, initiate that process
            if (currentConversation == ConversationType.ConceptBuilding) {
              logStatus("Auto-initiating concept building")
              speak("Data loaded.")
              // Auto-start concept building conversation
              scope.launch { initiateConceptBuilding() }
            }
          }
          DataLoadingState.Error -> {
            logStatus("Error loading concept data", StatusColor.Stop)
            // Optionally handle the error case
            speak("I'm having trouble loading the concept data. Let's try again later.")
          }
          DataLoadingState.Loading -> {
            logStatus("ConceptViewModel is still loading data")
            // Just wait, no action needed
          }
        }
      }
    } // data loading viewmodel.scope
  }

  private suspend fun toggleBrainConversation(isNetworkError: Boolean = false) {
    logStatus(
            "TBC: Entered toggleBrainConversation. isToggleInProgress: ${isToggleInProgress.get()}, conversationScope active: ${conversationScope != null}, currentConversation: $currentConversation",
            StatusColor.Default
    )
    // Ensure only one execution at a time
    if (!isToggleInProgress.compareAndSet(false, true)) {
      logStatus("TBC: Toggle already in progress, ignoring duplicate request", StatusColor.Pause)
      return
    }

    val initialState = dialogueState.value
    val initialConversation = currentConversation
    // Modified: Check both conversationScope and currentConversation
    val hasActiveConversation = conversationScope != null || currentConversation != null

    logStatus(
            "toggleBrainConversation called. DialogueState: $initialState, ConversationType: $initialConversation, Active conversation: $hasActiveConversation",
            StatusColor.Pause
    )

    try {
      if (hasActiveConversation) {
        // We have an active conversation - end it
        logStatus("Ending active conversation", StatusColor.Pause)

        // Stop the timer if it's running
        checkInTimerManager.stop()

        // Stop TTS and reset display state first
        interruptSpeech()
        agentCortex.updateDisplayState(DisplayState.None)

        // Only attempt to speak confirmation if not handling a network error
        if (!isNetworkError) {
          // Speak confirmation message BEFORE further cleanup
          // Direct use of TTS repo to avoid complex state management
          try {
            speak("Conversation ended.")
            logStatus("TBC: Successfully spoke confirmation message", StatusColor.Pause)
          } catch (e: Exception) {
            logStatus("TBC: Error speaking confirmation message: ${e.message}", StatusColor.Stop)
          }
        } else {
          logStatus("TBC: Skipping confirmation message due to network error", StatusColor.Stop)
        }

        // Explicitly set state to Idle after speaking confirmation
        agentCortex.updateDialogueState(DialogueState.Idle)

        // Type-specific cleanup based on conversation type
        when (initialConversation) {
          ConversationType.ConceptBuilding -> {
            // Concept Building specific cleanup
            agentCortex.updateConversationPlan(null) // Only reset plan for Concept Building
            logStatus("Concept Building conversation ended", StatusColor.Pause)
          }
          ConversationType.CoreLoop -> {
            // Core Loop specific cleanup
            val currentCoreLoopState = agentCortex.coreLoopState.value
            if (currentCoreLoopState.currentPhase != ConversationPhase.CHECK_IN) {
              // Keep the phase completion history but reset current phase to initial
              agentCortex.updateCoreLoopState(
                      phase = ConversationPhase.CHECK_IN,
                      understanding = "" // Clear the current understanding
              )
              logStatus("Core Loop state reset to initial assessment", StatusColor.Pause)
            }
          }
          else -> {
            logStatus("Unknown conversation type: $initialConversation", StatusColor.Stop)
          }
        }

        // Cancel the scope AFTER all other operations
        conversationScope?.cancel()
        conversationScope = null

        // Final shared cleanup
        updateConversationState(null)
        // State already set to Idle above

        logStatus("Brain conversation stopped, now in command mode", StatusColor.Pause)
      } else {
        // Start a new conversation
        // Check if microphone is inactive or paused, if so activate it
        ensureMicrophoneActive(voiceManager, ::logStatus)

        // Determine which type of conversation to start based on current screen
        if (currentScreen.value == Screen.Main) {
          // Start Core Loop conversation
          logStatus("Starting Core Loop conversation", StatusColor.Go)

          // Important: Update conversation type BEFORE updating dialogue state
          updateConversationState(ConversationType.CoreLoop)

          // Update dialogue state to ExpectingInput BEFORE launching the conversation
          // This ensures UI immediately reflects the conversation state
          updateDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))

          // Now launch the conversation
          scope.launch { handleCoreLoopIntent() }
        } else {
          // Start concept building conversation
          logStatus("Starting concept building conversation", StatusColor.Go)

          // Important: Update conversation type BEFORE updating dialogue state
          updateConversationState(ConversationType.ConceptBuilding)

          // Update dialogue state to ExpectingInput BEFORE launching the conversation
          updateDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))

          // Now launch the conversation
          scope.launch { initiateConceptBuilding() }
        }
      }
    } finally {
      // Important: Always reset the flag when done
      isToggleInProgress.set(false)
    }
  }

  private fun interruptSpeech() {
    interruptSpeech(ttsRepo, agentCortex, ::logStatus)
  }

  /** terminate the conversation with the brain */
  private fun terminateConversation() {
    logStatus(
            "TC: Entered terminateConversation. conversationScope active: ${conversationScope != null}",
            StatusColor.Default
    )
    // Cancel the entire conversation scope - this immediately stops all child coroutines
    conversationScope?.cancel()
    conversationScope = null

    // delete the current plan of action.  when we start a new conversation, this will be created
    // again.
    agentCortex.updateConversationPlan(null)

    // reset shared conversation variables
    resetConversation()

    // Reset states and release resources
    interruptSpeech()

    agentCortex.updateDisplayState(DisplayState.None)

    logStatus("Conversation terminated", StatusColor.Pause)
  }

  // Called when leaving ConceptScreen
  suspend fun onConceptScreenExit() {
    logStatus(
            "onConceptScreenExit called - terminating conversation and navigating away",
            StatusColor.Pause
    )
    terminateConversation()
    speak("Concept screen closed.")
    currentScreen.value = Screen.Main
    logStatus("Exiting concept screen")
    mainScreenState.clearSelection()

    agentCortex.updateConversationPlan(null)
    agentCortex.updateCurrentAction(ConceptActionState.INITIATE)
  }

  // this is triggered in the NavHost, just after the vm is created, then passed here
  // we can't receive it as an argument because agent is created earlier than vm (in app container)
  fun setVoiceManager(vm: VoiceManagedViewModel) {
    voiceManager = setVoiceManager(vm, agentCortex, scope, ::logStatus, ::handleVoiceInput)
  }

  private suspend fun handleVoiceInput(command: VoiceCommandEntity, text: String, timestamp: Long) {
    // this only triggers when dialogue state is ExpectingInput
    // logStatus("(handleVoiceInput) [${timestamp}] Voice Input")
    // logStatus("(handleVoiceInput) START - currentConversation=${currentConversation}")

    // compares incoming speech with previous agent utterance so it doesn't enter a self response
    // loop
    if (shouldIgnoreSpeech(text)) {
      // logStatus("Ignoring detected speech that contains agent's last utterance")
      return
    }

    /**
     * take precedence if we're in a brain / concept conversation filter input straight to brain so
     * that we don't interrupt natural speech with command keywords (actually, add text to
     * conversation history to buffer in preparation for sending to brain
     */
    if (currentConversation == ConversationType.ConceptBuilding ||
                    currentConversation == ConversationType.CoreLoop
    ) {
      // When in brain conversation and expecting input, feed everything directly to the brain
      handleUserResponse(text)
      return
    }

    // only process commands if we're not in an existing conversation
    if (currentConversation == null || command == VoiceCommandEntity.BACKGROUND) {
      when (command) {
        VoiceCommandEntity.STOP -> {
          logStatus("Stop command detected in idle mode", StatusColor.Pause)
          handleStopCommand(agentCortex, ::logStatus, ::toggleBrainConversation)
        }
        VoiceCommandEntity.SELECT -> {
          handleSelectCommand(
                  text,
                  repository,
                  agentCortex,
                  ::logStatus,
                  ::speak,
                  { commandMode.setSingleCommandMode(it) },
                  commandMode::selectWishNumber
          )
        }
        VoiceCommandEntity.DEFINE -> {
          handleDefineCommand(
                  text,
                  repository,
                  agentCortex,
                  ::speak,
                  navigationManager::navigateToConceptScreen,
                  { commandMode.setSingleCommandMode(it) }
          )
          // Clear main screen selection after navigation
          mainScreenState.clearSelection()
        }
        VoiceCommandEntity.START -> {
          // Route "start" command to Core Loop instead of legacy CommandMode
          if (agentCortex.conversationType.value == null) {
            speak("I received your command to start manifesting your wishes...")
            scope.launch { handleCoreLoopIntent(ConversationPhase.CHECK_IN) }
          } else {
            speak("We're already in a conversation. Say 'exit' to start again.")
          }
        }
        VoiceCommandEntity.READ -> {
          handleReadCommand(repository, ::speak)
        }
        // todo: initiate should only happen inside concept screen
        VoiceCommandEntity.INITIATE -> {
          handleInitiateCommand(
                  currentScreen.value,
                  agentCortex,
                  ::speak,
                  ::initiateConceptBuilding
          )
        }
        VoiceCommandEntity.AFFIRM -> {
          val currentWishes = allManifestations.first()
          affirmationManager.handleAffirmCommand(text, currentWishes, ::speak)
        }
        VoiceCommandEntity.QUIT -> {
          handleQuitCommand(::logStatus, ::speak)
        }
        VoiceCommandEntity.HELP -> {
          handleHelpCommand(text, ::speak)
        }
        VoiceCommandEntity.BACKGROUND -> processBackgroundInput(text)
        else -> {} // All other commands caught earlier
      }
    }
  }

  private suspend fun processBackgroundInput(text: String) {
    logStatus("Received voice input: $text")
    when (dialogueState.value) {
      // if agent is speaking, just ignore anything the user says! >:D
      // actually, this is to prevent the agent listening and responding to itself in a loop.
      is DialogueState.Speaking -> return

      // if we're expecting input, let's process it according to some rules
      is DialogueState.ExpectingInput -> {
        // conversation type is the primary classifier for handling input
        // either collecting a wish, or selecting actions to do with a wish, or concept building

        when (currentConversation) {
          is ConversationType.CommandMode -> {
            // Route all CommandMode conversations to the CommandMode class
            commandMode.processInput(text, dialogueState.value)
          }
          else ->
                  logStatus(
                          "Trying to process input on some type of conversation I don't know about"
                  )
        }
      }
      else -> {
        // logStatus("Unexpected dialogue state: ${dialogueState.value}")
      }
    }
  }

  suspend fun speak(message: String) {
    speak(message, ttsRepo, agentCortex, ::logStatus)
  }

  private suspend fun enterConversationLoop() {
    // Delegate to CommandMode for all conversation loop logic
    logStatus("Delegating conversation loop to CommandMode")
    commandMode.enterConversationLoop()
  }

  // this is called by the voice recog process when we're in a concept building conversation
  // all we need to do is to add the latest response to the conversation history,
  private fun handleUserResponse(text: String) {
    // Add to conversation history - the voice processor no longer handles this
    val currentPhase = agentCortex.coreLoopState.value.currentPhase
    addToHistory(Speaker.User, text, currentPhase)
  }

  private suspend fun processUserResponse() {
    val rawText =
            com.example.voxmanifestorapp.ui.agent.voice.processUserResponse(
                    voiceManager,
                    agentCortex,
                    ::logStatus
            )
                    ?: return

    // Handle differently based on conversation type
    when (currentConversation) {
      ConversationType.CoreLoop -> {
        // For Core Loop, use the dedicated handler
        handleCoreLoopResponse(rawText)
      }
      ConversationType.ConceptBuilding -> {
        // For Concept Building, use the existing concept screen logic
        currentConceptViewModel?.getConceptBuildingContext()?.let { context ->
          // Add to conversation history first
          handleUserResponse(rawText)
          // Then get next decision from brain
          getNextBrainDecision(context, createToolLibrary())
        }
                ?: run { logStatus("Failed to get concept context", StatusColor.Stop) }
      }
      else -> {
        logStatus("Conversation type not handled: $currentConversation", StatusColor.Stop)
      }
    }
  }

  private fun createToolLibrary(): ConceptToolLibrary {
    return currentConceptViewModel?.let {
      ConceptToolLibrary(agent = this, conceptViewModel = it, conceptRepository = conceptRepository)
    }
            ?: throw IllegalStateException("Cannot create tool library: concept view model is null")
  }

  /**
   * Initiates the concept building process, sets up initial prompt & conversation history Normally
   * loaded from observeConceptViewModel() -> triggered when concept screen is loaded. Sets up state
   * variables, launches the conversation scope & loop between agent <-> brain service
   * initiateConceptBuilding(): leads to -->
   * ```
   *      getNextBrainDecision() - Makes API calls to the LLM
   *      Tool execution - e.g., QuestionTool.execute()
   *      Listening for user responses
   *      Processing responses and making further decisions
   * ```
   * ::: All should be cancellable by toggleConversation() ^^ by quitting the conversation scope set
   * up here.
   */
  suspend fun initiateConceptBuilding() {
    val agentId = System.identityHashCode(this)
    Log.d("AgentDebug", "Agent $agentId initiating concept building")

    /** Reset variables for concept building */
    agentCortex.updateConversationPlan(null)

    // Only create new scope if none exists (preserve existing core loop scope)
    if (conversationScope == null) {
      conversationScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    }
    logStatus("INITIATING CONCEPT BUILDING>>>")

    /** launch of conversation scope process for management and interruptions of conversations */
    conversationScope?.launch {
      try {
        currentConceptViewModel?.let { viewModel ->
          val toolLibrary = createToolLibrary()

          Log.d("CONTEXT", "About to gather context..")

          // get information from the concept nexus about current wish and concepts to be worked
          // with.
          val context = viewModel.getConceptBuildingContext()
          Log.d("CONTEXT", "Context should have been gathered from viewmodel now.")

          if (context == null) {
            speak("I'm sorry, I can't establish adequate context to initiate this process.")
            logStatus("ERROR: Context variable couldn't initialise.")
            return@launch
          }

          // get initial setup from brain
          val initialPrompt = getInitialPrompt(context, toolLibrary)

          // add this prompt to very beginning of conversation history, so brain always starts with
          // adequate context.
          addToHistory(
                  speaker = Speaker.Agent,
                  content = initialPrompt,
                  phase = agentCortex.coreLoopState.value.currentPhase,
                  isVisible = false // hide system prompts from UI
          )

          /*  let's not show this now as its clogging up the logs.
          logStatus(
              "*** Crafted initial prompt to brain & front-loaded into conversation history: \n" +
                      "${initialPrompt}"
          )

           */

          // send initial prompt to brain and process response.
          // brain needs: context, toolLibrary, currentPlan...
          // todo: do we need currentPlan to be state variable?!?
          getNextBrainDecision(context, toolLibrary)
        }
                ?: run {
                  logStatus("Couldn't initiateConceptBuilding(): concept view model not found")
                  return@launch
                }
      } catch (e: CancellationException) {
        /** execute here when the conversation scope process is cancelled */
        logStatus("Concept building was cancelled", StatusColor.Pause)
      } catch (e: Exception) {
        logStatus("getNextBrainDecision() failed: Exception $e")
      }
    } // conversation scope close bracket
  }

  /**
   * Collects and delivers information about the current state of the concept screen to the Brain.
   * Request initial guidance from brain service Begins a conversation with the user based on the
   * brain's decision
   */
  private suspend fun getNextBrainDecision(
          context: ConceptBuildingContext,
          toolLibrary: ConceptToolLibrary,
  ) {

    // first we should craft the working prompt
    val prompt = getWorkingPrompt(context, toolLibrary)

    // Get initial decision from brain (send context and tool library from here)
    // Update to Thinking state while waiting for the LLM
    agentCortex.updateDialogueState(DialogueState.Thinking)
    var brainDecision = brainService.getNextConceptAction(prompt)
    // Return to appropriate state after getting response
    agentCortex.updateDialogueState(DialogueState.Speaking)

    logStatus("Obtained a brainDecision!!! \n${brainDecision}")

    // analyses the brain's result based on whether we were successful or not
    brainDecision.fold(
            onSuccess = { decision ->
              // BrainDecision(toolname, parameters, action) returned from brain service
              agentCortex.updateConversationPlan(decision.plan)

              logStatus(
                      buildString {
                        append(
                                """
                        Updated conversation plan: Goal: ${decision.plan.goal}
                        Steps:
                        ${
                            decision.plan.steps.mapIndexed { index, step ->
                                """
                            Step ${index + 1}:
                              Intent: ${step.intent}
                              Description: ${step.description}""".trimIndent()
                            }.joinToString("\n")
                        }
                        Current step: ${decision.plan.currentStepIndex + 1}/${decision.plan.steps.size}
                    """.trimIndent()
                        )
                      }
              )

              // Execute the chosen tool
              val tool =
                      toolLibrary.getTool(decision.toolName)
                              ?: run {
                                logStatus("Tool ${decision.toolName} not found", StatusColor.Stop)
                                speak("I encountered an error with my tools. Let's try again.")
                                return@fold
                              }

              // Execute tool and handle result
              when (val result = toolLibrary.executeTool(decision)) {
                is ToolResult.Success -> {
                  // Update our brain conversation state
                  agentCortex.updateCurrentAction(decision.action)

                  /**
                   * looks like we need to simply feed whatever reply the llm supplies us with and
                   * wait for the user to communicate back to the llm. the llm can then decide if it
                   * wants to do an action while maintaining the conversation.
                   */
                }
                is ToolResult.Error -> {
                  logStatus("Tool execution failed: ${result.message}", StatusColor.Stop)
                  speak("I encountered an error. Let's try a different approach.")
                  // Could add retry logic here if needed
                  resetConversation()
                }
              }
            },
            onFailure = { error ->
              logStatus("Brain decision error: ${error.message}", StatusColor.Stop)
              speak("I'm sorry.  My brain isn't working.  Let's try again in a moment.")
              resetConversation()
            }
    )
  }

  /**
   * initial prompt sets up everything for the brain: how to communicate, working through stages in
   * the conversation, planning the conversation, using tools, etc.
   */
  fun getInitialPrompt(context: ConceptBuildingContext, toolLibrary: ConceptToolLibrary) =
          """
        * Overall Background & Context. *
        
          You are a manifestation genie inside the 'vox manifestor app'
           You help the user manifest their wishes using the methods and communication style
        of an expert coach and consultant to help users develop their wishes.
        
         By helping the user think through these component ideas and store them in this app, we are helping
         them mentally bring themselves closer to the achievement of their wishes.
         
        Your communication style reflects the precision and insight of a highly skilled coach:
        - Clear, professional language that draws out specific insights
        - Questions that help users articulate vague wishes into concrete concepts
        - Systematic approach to understanding the process of achieving goals
        
        The user has just entered a 'concept screen' within the app.  On this screen you will help the user edit
        different concepts.  Here is a summary of the current concept you are working on:
        
        * Current Concepts You Are Helping User With *
        
        State Transformation:
          Description: ${ConceptType.StateTransformation.description}
          Purpose: ${ConceptType.StateTransformation.purpose}
          Guidance: ${ConceptType.StateTransformation.guidance}
        
        Component States:
        
        1. Present State (Current Reality):
          Description: ${ConceptType.Present.description}
          Purpose: ${ConceptType.Present.purpose}
          Guidance: ${ConceptType.Present.guidance}
          Required Items: ${ConceptType.Present.requiredItems}
        
        2. Desired State (Future Vision):
          Description: ${ConceptType.Desired.description}
          Purpose: ${ConceptType.Desired.purpose}
          Guidance: ${ConceptType.Desired.guidance}
          Required Items: ${ConceptType.Desired.requiredItems}
        
        Concepts in this app are made up of a number of text descriptions (usually 3, but may vary) by the user that
         describe the relevant component of the current manifestation.
         
        We want to establish a single sentence or AT MOST two sentences for each item within a concept.
        Don't try to extract too much detail when gathering these sentences.
        
        * Planning and executing a goal-oriented conversation: *

        These are the potential concept action states.  The idea is you move sequentially through these, and use
        them as a framework to guide your conversation with the user.

          ${ConceptActionState.getActionDescriptions()}

        You should also inform the agent what action decision you are making and set the current state with the 
        action key.
        
                Conversationally, try to be concise, and tend toward multi-turn conversations rather than long diatribes.
        Do not ask more than one question within a single conversational 'turn', or try to address more than one
        topic at once.
        
        If you want to make multiple points, make them one
        step at a time, while awaiting user feedback / confirmation / response after each one.
        
        You can plan up to 5 steps in the current conversation.  This can help you keep track of where you're at in a
        relatively complex, multi-turn conversation.
    
        Each of your responses must include a complete conversation plan, either:
            - The existing plan with updated current step index
            - A new plan if none exists or current plan needs revision
    
        The goal reminds you the overall outcome youre trying to achieve.
        The steps are how to get there, each step usually being a single relatively simple utterance.
        The description describes what communication you want to do, and the intent explains the purpose of that message.
         Remember the user will be hearing your words translated into a voice, so keep it short and to the point.
        You should update the currentStepIndex as you work through the conversation plan.

        Once you complete the final step in this sequence, you can start another conversation sequence or simply go idle,
        informing the user that this is what you are doing.

        * Using tools to help you achieve your goal with the user *
        
        You have a list of tools you can use to help the user fill these out.  If possible, when querying the user, 
        aim to have the user list out the elements of the concept in one chunk, which you can then analyse and
        break down into the requisite number of items.  If, however the user has not presented enough information,
        or you think they need some more clarity in what they are saying, feel free to ask them to continue
        telling you about the item, and use the conversation history below to save as detailed concept info as you can.
        
        Try your best to separate different issues / concerns out into different concept items.  Sometimes you will have to discard
        something that has been mentioned in order to ensure you only represent one idea within one concept item.  You will have
        to decide which 3 ideas seem most important and discard the rest.

        Do not make your questions too long if possible.  Be succinct and gather information over multiple questioning rounds
        rather than trying to do everything all at once.
        
        Available tools:
        ${toolLibrary.getToolDescriptions()}
        
        *** Response format ***
        This is the response format we require, including the planning steps and tool details, every time.
        Even if the plan doesn't change, you should still send your latest understanding of the plan each time.
        
        {
            "plan": {
                "goal": string,
                "steps": [
                    {
                        "description": string,
                        "intent": string
                    }
                ],
                "currentStepIndex": number,
            }
            toolName: [TOOL_NAME]
                parameters: {
                   param1: value1
                   param2: value2
                },
            action: [CONCEPT_ACTION_STATE]

        }

        * Additional guidance on working through conversational stages *
        
        When all concept items are filled (Present State has ${ConceptType.Present.requiredItems} items and 
        Desired State has ${ConceptType.Desired.requiredItems} items), proceed directly to analyzing the relationship 
        between the concepts. Use the questionTool to present your analysis and ask the user if they want to make any 
        adjustments based on the relationships you've identified.
        
        Remember: Always use the questionTool when expecting a response from the user, even during analysis.
        Your analysis should focus on:
        1. How well each Present State item corresponds to a Desired State item
        2. Whether the transformations between states are clear and achievable
        3. Any potential gaps or unclear relationships that might need refinement
        
        Only ask for clarification when you yourself have obtained some kind of insight.  Don't ask the user for additional
        insights about the relationship between present and desired state.  This is your job to figure out.
        Look at the 3 analysis points above and determine any obvious problems with the user's present or desired state definitions.
        If it looks reasonable, don't be afraid to end the conversation by saying something like 
        
        "This looks good.  Let me know if you need anything else."

        Example analysis structure:
        {
            "plan": { ... <plan json details not shown here> ... }

            "toolName": "askQuestion",
            "parameters": {
                "question": "I've analyzed the relationships between your states: [brief analysis]. I'd like to suggest [your suggestion (only 1 at once)"
            },
            "action": "ANALYZE_CONFIRM"
        }
        
        * Current Status of Concepts and their Contents, related to Current Wish: (${context.manifestationTitle}) *
        ${
            context.conceptData.entries.joinToString("\n") { (type, items) ->
                val required = type.requiredItems ?: 0
                buildString {
                    append("${type::class.simpleName}: ${items.size}/$required items\n")
                    // Add completion status
                    val isComplete = items.size >= required
                    append("Status: ${if (isComplete) "(COMPLETE)" else "(INCOMPLETE)"}\n")
                    items.forEachIndexed { index, item ->
                        append("  ${index + 1}. ${item.content}\n")
                    }
                }
            }
        }
        
    Do NOT add any quotes or text before the opening bracket { or after the closing bracket } at the beginning and end of your response!
        
    """.trimIndent()

  /**
   * the working prompt is sent each time we prompt the brain, as it provides valuable updates to
   * the app's existing state
   */
  private fun getWorkingPrompt(context: ConceptBuildingContext, toolLibrary: ConceptToolLibrary) =
          """
            Current Concept Status for wish '${context.manifestationTitle}':
    ${
        context.conceptData.entries.joinToString("\n") { (type, items) ->
            buildString {
                append("${type::class.simpleName}: ${items.size}/${type.requiredItems} items\n")
                items.forEachIndexed { index, item ->
                    append("  ${index + 1}. ${item.content}\n")
                }
            }
        }
    }
    
    Current Action State: $currentAction
    
    Current Conversation Plan:
    ${
        conversationPlan.value?.let { plan ->
            """
        Goal: ${plan.goal}
        Progress: Step ${plan.steps + 1} of ${plan.steps.size}
        
        Steps:
        ${
                plan.steps.mapIndexed { index, step ->
                    """
            Step ${index + 1}: ${if (index == plan.currentStepIndex) ">>> CURRENT <<<" else ""}
            Description: ${step.description}
            Intent: ${step.intent}
            """.trimIndent()
                }.joinToString("\n\n")
            }
        """
        } ?: "No active conversation plan"
    }
    
        * Conversation History *
        ${
        buildString {
            if (conversationHistory.value.isNotEmpty()) {
                append("\nConversation History:\n")
                conversationHistory.value.forEach { entry ->
                    append("${entry.speaker}: ${entry.content}\n")
                    append("(State: ${entry.phase})\n")
                }
            }
        }
    }
        
    }
    """.trimIndent()

  suspend fun askAndListen(question: String) {

    speak(question)
    // add the question to the conversation history
    addToHistory(Speaker.Agent, question, agentCortex.coreLoopState.value.currentPhase)

    // change dialogue state within agent cortex
    changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
  }

  // reflexive agent (fixed state-machine) conversation functions
  private fun updateConversationState(newState: ConversationType?) {
    agentCortex.updateConversationType(newState)
    logStatus("Conversation state updated to $newState")
  }

  // updateCurrentStep removed - now handled by CommandMode
  fun updateDialogueState(newState: DialogueState) {
    agentCortex.updateDialogueState(newState)
    // logStatus("Current dialogue state is $newState")
  }

  // Delegate to AgentCortex for dialogue state changes
  private fun changeDialogueState(state: DialogueState) {
    agentCortex.updateDialogueState(state)
  }

  private fun resetConversation() {
    // Stop timer and reset first response flag
    checkInTimerManager.stop()
    isFirstResponse = true

    // Use AgentCortex for conversation reset
    agentCortex.resetConversationState()

    // reset internal conversation state vars
    updateConversationState(null)
    updateDialogueState(DialogueState.Idle)

    // clear the main screen's current slot index through the API, if mainscreen active.
    if (currentScreen.value == Screen.Main) {
      mainScreenState.clearSelection()
    }
  }

  // Legacy functions removed - now handled by CommandMode

  // handleProcessSelection removed - now handled by CommandMode

  // askForWish, captureWish, checkWish removed - now handled by CommandMode

  // selectWishNumber and selectWishBySlot removed - now handled by CommandMode

  // Utility functions removed - now handled by CommandMode and ConversationUtilities

  suspend fun closeConceptScreen() {
    onConceptScreenExit()

    withContext(Dispatchers.Main) { navigationManager.navigateBack() }
  }

  /** CORE LOOP CODE BEGINS */

  /**
   * Handles Core Loop intent requests from the UI. This determines whether to initiate the loop for
   * the first time or resume an existing conversation.
   */

  // todo: manage the 'startphase' argument - depending on initiate or resume.

  private suspend fun handleCoreLoopIntent(
          startPhase: ConversationPhase = ConversationPhase.CHECK_IN
  ) {
    logStatus("Core Loop intent received for phase: $startPhase", StatusColor.Go)

    // Set conversation state
    updateConversationState(ConversationType.CoreLoop)

    // Set up conversation scope for proper management and cancellation
    conversationScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

    // Launch the progression within the conversation scope
    conversationScope?.launch {
      try {

        /** Begin the core loop */
        progressCoreLoop()
      } catch (e: CancellationException) {
        // Handle cancellation of the Core Loop
        logStatus("Core Loop conversation was cancelled", StatusColor.Pause)
      } catch (e: Exception) {
        // Handle other exceptions
        logStatus("Exception in Core Loop: ${e.message}", StatusColor.Stop)
      }
    }
  }

  /**
   * Main progression function for the Core Loop. Examines current state, determines next steps, and
   * advances the loop.
   */
  private suspend fun progressCoreLoop() {
    // Get current state
    val currentState = agentCortex.coreLoopState.value
    val currentPhase = currentState.currentPhase

    logStatus("Processing Core Loop phase: $currentPhase", StatusColor.Default)

    if (currentPhase == ConversationPhase.CHECK_IN) {
      // Check-In orchestration remains in ConversationAgent (proven pattern)
      progressCheckIn()
    } else {
      // Delegate Core Loop processing to CoreLoopManager
      // Keep the existing behavior - just move the functions
      changeDialogueState(DialogueState.Thinking)

      try {
        // Create prompt and process with LLM via CoreLoopManager
        val prompt = coreLoopManager.createCoreLoopPrompt(currentPhase)
        val result = brainService.getCoreLoopGuidance(prompt)

        changeDialogueState(DialogueState.Speaking)

        result.fold(
                onSuccess = { response ->
                  // Speak the LLM response directly
                  speak(response)
                  changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
                },
                onFailure = { error ->
                  logStatus("Error getting Core Loop guidance: ${error.message}", StatusColor.Stop)
                  speak(
                          "I'm having difficulty processing our conversation. Let's try again in a moment."
                  )
                  changeDialogueState(DialogueState.Idle)
                  delay(2000)
                  progressCoreLoop()
                }
        )
      } catch (e: Exception) {
        logStatus("Exception in Core Loop processing: ${e.message}", StatusColor.Stop)
        changeDialogueState(DialogueState.Idle)
        speak("I'm experiencing a technical issue. Let's take a moment and try again.")
      }
    }
  }

  /** Main orchestration method for check-in phase - Using direct exception handling */
  private suspend fun progressCheckIn() {
    logStatus("🔄 CHECK-IN AGENT: Initiating progressCheckIn()", StatusColor.Default)
    changeDialogueState(DialogueState.Thinking)

    // Start timer on first response
    if (isFirstResponse) {
      logStatus("🔄 CHECK-IN AGENT: First response detected, starting timer", StatusColor.Default)
      checkInTimerManager.start()
      isFirstResponse = false
    }

    // Use direct try-catch instead of the adapter pattern
    val turnProcessedSuccessfully =
            try {
              // Step 1: Evaluate transition with basic wish data
              val basicWishes = fetchUserWishesForContext(repository, ::logStatus)

              /**
               * we receive a TransitionEvaluationResult back that we need for later on
               * shouldTransition: Boolean, extractedThemes: List<ConversationalTheme>,
               * updatedMetrics: UserEngagementMetrics, transitionReasoning: String = ""
               */
              val evaluationResult = checkInDialogueChain.evaluateTransition(basicWishes)

              // SHARED OPERATION: Update central state with new themes and metrics
              /**
               * conversation agent must update central state with themes and metrics from current
               * turn
               */
              agentCortex.updateActiveThemes(evaluationResult.extractedThemes)
              agentCortex.updateEngagementMetrics(evaluationResult.updatedMetrics)
              logStatus(
                      "🔄 CHECK-IN AGENT: Updated active themes and engagement metrics.",
                      StatusColor.Default
              )

              /** BEGIN TRANSITION AWAY FROM CHECK-IN */
              if (evaluationResult.shouldTransition) {
                // Step 2a: Transition path - fetch enhanced wish data and call TransitionChain
                logStatus(
                        "🔄 CHECK-IN AGENT: Transitioning - fetching enhanced wish data",
                        StatusColor.Go
                )
                val enhancedWishes =
                        fetchEnhancedWishDataForTransition(
                                repository,
                                conceptRepository,
                                ::logStatus
                        )

                // Call TransitionChain with enhanced data and direct state access
                logStatus(
                        "🔄 CHECK-IN AGENT: Calling TransitionChain with enhanced wishes and direct parameters",
                        StatusColor.Go
                )
                Log.d(TAG, "🔄 CHECK-IN AGENT: About to call transitionChain.processTransition()")
                Log.d(
                        TAG,
                        "🔄 CHECK-IN AGENT: Input themes count: ${evaluationResult.extractedThemes.size}"
                )
                Log.d(TAG, "🔄 CHECK-IN AGENT: Input enhanced wishes count: ${enhancedWishes.size}")

                val transitionActionPlan =
                        transitionChain.processTransition(
                                themes = evaluationResult.extractedThemes,
                                enhancedWishes = enhancedWishes
                        )

                // Comprehensive logging of received TransitionActionPlan
                Log.d(
                        TAG,
                        "🔄 CHECK-IN AGENT: ✅ RECEIVED TransitionActionPlan from TransitionChain"
                )
                Log.d(
                        TAG,
                        "🔄 CHECK-IN AGENT: Action suggestion length: ${transitionActionPlan.actionSuggestion.length} chars"
                )
                Log.d(
                        TAG,
                        "🔄 CHECK-IN AGENT: Action suggestion: ${transitionActionPlan.actionSuggestion}"
                )
                Log.d(
                        TAG,
                        "🔄 CHECK-IN AGENT: Proposed phase: ${transitionActionPlan.proposedPhase}"
                )
                Log.d(
                        TAG,
                        "🔄 CHECK-IN AGENT: Target wish ID: ${transitionActionPlan.targetWishId}"
                )
                Log.d(TAG, "🔄 CHECK-IN AGENT: Reasoning: ${transitionActionPlan.reasoning}")
                Log.d(TAG, "🔄 CHECK-IN AGENT: =====================================")

                logStatus(
                        "🔄 CHECK-IN AGENT: ✅ Successfully received TransitionActionPlan from TransitionChain",
                        StatusColor.Go
                )

                /**
                 * We've received the proposed action plan from the Transition Chain. We need to
                 * feed it back to the user and see if they want to follow our suggestions.
                 */
                handleStructuredTransition(transitionActionPlan)
              } else {

                /** CONTINUE CHECK-IN CONVERSATION */

                // Step 2b: Continue conversation path
                logStatus("🔄 CHECK-IN AGENT: Continuing conversation", StatusColor.Default)
                val response = checkInDialogueChain.continueCheckIn(evaluationResult, basicWishes)

                // Handle continue conversation using existing method
                handleContinueCheckIn(response)
              }

              // If we get here, processing was successful
              true
            } catch (e: Exception) {
              // Use centralized error handling as documented in checkin_error_handling.md
              logStatus(
                      "🔄 CHECK-IN AGENT: Exception caught during dialogue turn: ${e.message}",
                      StatusColor.Stop
              )

              // Delegate to centralized error handling
              handleError(
                      error = e,
                      onFailure = null,
                      toggleConversation = ::toggleBrainConversation,
                      speak = ::speak,
                      updateDialogueState = { state -> changeDialogueState(state) },
                      agentCortex = agentCortex,
                      logger = ::logStatus
              )

              // Processing was not successful
              false
            }

    if (!turnProcessedSuccessfully) {
      logStatus(
              "🔄 CHECK-IN AGENT: Dialogue turn processing was not successful (error handled).",
              StatusColor.Stop
      )
    }
  }

  // Handler for continue check-in conversation
  private suspend fun handleContinueCheckIn(response: Response) {
    logStatus("🔄 CHECK-IN AGENT: Continuing check-in conversation.", StatusColor.Default)
    try {
      // Get current themes from AgentCortex
      val currentThemes = agentCortex.checkInState.value.activeThemes

      // Log theme information before storage
      Log.d(TAG, "🔄 CHECK-IN AGENT: Storing themes in metadata for agent response")
      Log.d(TAG, "🔄 CHECK-IN AGENT: Current themes count: ${currentThemes.size}")
      currentThemes.forEachIndexed { index, theme ->
        Log.d(
                TAG,
                "🔄 CHECK-IN AGENT: Theme ${index + 1}: '${theme.title}' with ${theme.observations.size} observations"
        )
      }

      // Store themes in metadata - serialize once here, avoid double serialization
      Log.d(TAG, "🔄 CHECK-IN AGENT: Storing ${currentThemes.size} themes in metadata")
      currentThemes.forEachIndexed { index, theme ->
        Log.d(TAG, "🔄 CHECK-IN AGENT: Theme ${index + 1}: '${theme.title}' with ${theme.observations.size} observations")
      }

      // Serialize themes to JsonElement for storage in metadata
      val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
        coerceInputValues = true
      }
      val themesJsonElement = json.encodeToJsonElement(kotlinx.serialization.serializer<List<ConversationalTheme>>(), currentThemes)

      Log.d(TAG, "🔄 CHECK-IN AGENT: Themes JsonElement: $themesJsonElement")

      // Store the metadata using JsonElement
      val metadata =
              mapOf(
                      "themes" to themesJsonElement,
                      "strategy" to JsonPrimitive(response.strategy.name),
                      "reasoning" to JsonPrimitive(response.reasoning),
                      "sessionContext" to JsonPrimitive("check_in_continuation")
              )

      Log.d(TAG, "🔄 CHECK-IN AGENT: Adding agent response to history with metadata")
      Log.d(TAG, "🔄 CHECK-IN AGENT: Metadata keys: ${metadata.keys.joinToString(", ")}")
      Log.d(TAG, "🔄 CHECK-IN AGENT: Strategy: ${response.strategy.name}")
      Log.d(TAG, "🔄 CHECK-IN AGENT: Response length: ${response.response.length} chars")

      addToHistory(
              Speaker.Agent,
              response.response,
              phase = agentCortex.coreLoopState.value.currentPhase,
              metadata = metadata
      )

      Log.d(
              TAG,
              "🔄 CHECK-IN AGENT: ✅ Successfully added agent response to history with themes in metadata"
      )
      speak(response.response)
      changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
    } catch (e: Exception) {
      logStatus(
              "🔄 CHECK-IN AGENT: ❌ Error while handling continue conversation: ${e.message}",
              StatusColor.Stop
      )
      e.printStackTrace()
      speak("I had a problem with that last step. Let's try again.")
      changeDialogueState(DialogueState.Idle) // Fallback to Idle
    }
  }

  /**
   * Standardized function for all core loop phase transitions. All state changes flow through here
   * to ensure consistency and narrative continuity.
   */
  private suspend fun navigateCoreLoopPhase(
          actionPlan: TransitionActionPlan
  ) {
    val targetPhase = actionPlan.getConversationPhase()

    if (targetPhase != null) {
      logStatus("🔄 CORE LOOP: Navigating to phase: ${targetPhase.name}", StatusColor.Go)

      // 1. Update core loop state (THE centralized state change point)
      agentCortex.updateCoreLoopState(
              phase = targetPhase,
              wishIndex = actionPlan.targetWishId ?: -1,
              understanding = actionPlan.reasoning
      )

      // 2. Log transition for narrative continuity
      addToHistory(
              Speaker.Agent,
              "Transitioning to ${targetPhase.description}",
              phase = targetPhase,
              metadata =
                      mapOf(
                              "transitionReasoning" to JsonPrimitive(actionPlan.reasoning),
                              "targetWishId" to JsonPrimitive(actionPlan.targetWishId?.toString() ?: "null"),
                              "proposedPhase" to JsonPrimitive(actionPlan.proposedPhase),
                              "actionSuggestion" to JsonPrimitive(actionPlan.actionSuggestion),
                              "navigationDecision" to JsonPrimitive("core_loop_phase_transition")
                      )
      )

      // 3. Route to appropriate implementation
      when (targetPhase) {
        ConversationPhase.PRESENT_STATE_EXPLORATION,
        ConversationPhase.DESIRED_STATE_EXPLORATION -> {
          launchConceptScreenForPhase(actionPlan.targetWishId, targetPhase)
        }
        else -> {
          // Continue with main conversation flow for other phases
          changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
        }
      }
    } else {
      // Invalid phase - throw exception for centralized error handling
      throw IllegalStateException(
              "Invalid phase in TransitionActionPlan: ${actionPlan.proposedPhase}"
      )
    }
  }

  /** Launches concept screen as implementation of specific core loop phases */
  private suspend fun launchConceptScreenForPhase(wishId: Int?, phase: ConversationPhase) {
    if (wishId != null) {
      val wish = repository.getManifestationById(wishId)
      if (wish != null) {
        // Set conversation type for concept building
        agentCortex.updateConversationType(ConversationType.ConceptBuilding)

        speak("Let's work on ${phase.description.lowercase()} for ${wish.title}")

        withContext(Dispatchers.Main) { navigationManager.navigateToConceptScreen(wish.id) }
        // Concept screen will auto-initiate and access phase context via CoreLoopState
      } else {
        logStatus("⚠️ Wish with ID $wishId not found, continuing with core loop", StatusColor.Pause)
        changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
      }
    } else {
      logStatus("⚠️ No wish ID provided for concept screen phase", StatusColor.Pause)
      changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
    }
  }

  /**
   * Handles structured transition using TransitionActionPlan for intelligent routing. Uses the
   * structured data to route to the correct phase with proper context.
   */
  private suspend fun handleStructuredTransition(actionPlan: TransitionActionPlan) {
    logStatus(
            "🔄 CHECK-IN AGENT: Structured transition complete. Target phase: ${actionPlan.proposedPhase}, Target wish: ${actionPlan.targetWishId}",
            StatusColor.Go
    )

    // Stop the timer as we are leaving the check-in phase
    checkInTimerManager.stop()

    // Update AgentCortex with themes from TransitionActionPlan
    if (actionPlan.themes.isNotEmpty()) {
      agentCortex.updateActiveThemes(actionPlan.themes)
      Log.d(TAG, "🔄 CHECK-IN AGENT: Updated AgentCortex with ${actionPlan.themes.size} themes from TransitionActionPlan")
      logStatus("🔄 CHECK-IN AGENT: Updated AgentCortex with themes from transition", StatusColor.Default)
    }

    // Speak the transition message
    if (actionPlan.actionSuggestion.isNotBlank()) {
      speak(actionPlan.actionSuggestion)

      // Add to history with phase reasoning preserved in metadata
      val currentThemes = agentCortex.checkInState.value.activeThemes
      val themesJsonElement = if (currentThemes.isNotEmpty()) {
        val json = Json {
          ignoreUnknownKeys = true
          isLenient = true
          coerceInputValues = true
        }
        json.encodeToJsonElement(kotlinx.serialization.serializer<List<ConversationalTheme>>(), currentThemes)
      } else null

      val transitionMetadata = mutableMapOf<String, JsonElement>()
      transitionMetadata["phaseReasoning"] = JsonPrimitive(actionPlan.reasoning)
      transitionMetadata["suggestedPhase"] = JsonPrimitive(actionPlan.proposedPhase)
      transitionMetadata["targetWishId"] = JsonPrimitive(actionPlan.targetWishId?.toString() ?: "null")
      transitionMetadata["transitionType"] = JsonPrimitive("structured_transition")

      // Add themes to metadata if available
      themesJsonElement?.let { themes ->
        transitionMetadata["themes"] = themes
        Log.d(TAG, "🔄 CHECK-IN AGENT: Added ${currentThemes.size} themes to transition metadata")
      }

      Log.d(TAG, "🔄 CHECK-IN AGENT: Adding transition message to history with metadata")
      Log.d(
              TAG,
              "🔄 CHECK-IN AGENT: Transition metadata keys: ${transitionMetadata.keys.joinToString(", ")}"
      )
      Log.d(
              TAG,
              "🔄 CHECK-IN AGENT: Action suggestion length: ${actionPlan.actionSuggestion.length} chars"
      )

      addToHistory(
              Speaker.Agent,
              actionPlan.actionSuggestion,
              phase = agentCortex.coreLoopState.value.currentPhase,
              metadata = transitionMetadata
      )

      // Log the metadata preservation
      Log.d(
              TAG,
              "🔄 CHECK-IN AGENT: ✅ Successfully added transition message to history with metadata"
      )
      Log.d(TAG, "🔄 CHECK-IN AGENT: Metadata - phaseReasoning: ${actionPlan.reasoning}")
      Log.d(TAG, "🔄 CHECK-IN AGENT: Metadata - suggestedPhase: ${actionPlan.proposedPhase}")
      Log.d(TAG, "🔄 CHECK-IN AGENT: Metadata - targetWishId: ${actionPlan.targetWishId}")
    }

    // Update session name if provided by TransitionChain
    actionPlan.sessionName?.let { sessionName ->
      try {
        val currentSessionId = conversationRepository.getCurrentSessionId()
        conversationRepository.updateSessionName(currentSessionId, sessionName)
        Log.d(TAG, "🔄 CHECK-IN AGENT: Updated session name to: '$sessionName'")
        logStatus("🔄 CHECK-IN AGENT: Session named: '$sessionName'", StatusColor.Default)
      } catch (e: Exception) {
        Log.e(TAG, "Error updating session name: ${e.message}", e)
        logStatus("⚠️ Failed to update session name: ${e.message}", StatusColor.Pause)
      }
    }

    // Use the standardized core loop navigation function
    navigateCoreLoopPhase(actionPlan)
  }

  /** Creates context information about why a wish was selected */
  private suspend fun createWishSelectionContext(wishIndex: Int, reasonCode: String): String {
    return buildString {
      try {
        val manifestation = repository.getManifestationBySlot(wishIndex)
        if (manifestation != null) {
          append("\n>>> IMPORTANT: THIS WISH WAS JUST SELECTED <<<\n")

          // Format the date of last update
          val lastUpdatedDate =
                  manifestation.lastDiscussedTimestamp?.let { timestamp ->
                    val date = SimpleDateFormat("MMMM d, yyyy", Locale.US).format(Date(timestamp))
                    "last updated on $date"
                  }
                          ?: "has never been updated since creation"

          val selectionReason =
                  when (reasonCode) {
                    "empty_slot" ->
                            "This is an empty wish slot that needs to be filled with a new wish."
                    "missing_present_state" ->
                            "This wish needs exploration of the present state, which hasn't been defined yet."
                    "missing_desired_state" ->
                            "This wish has a defined present state but needs exploration of the desired outcome."
                    "ready_for_contrast" ->
                            "This wish has both present and desired states defined and is ready for contrast analysis."
                    "least_recently_updated" ->
                            "This wish was selected because it $lastUpdatedDate, longer than other wishes."
                    else -> "This wish was selected based on the system's prioritization algorithm."
                  }

          append("- Selection reason: $selectionReason\n")
          append(
                  "- When introducing this wish to the user, explain why it was chosen and guide them into the appropriate phase.\n"
          )
          append(">>> END IMPORTANT SECTION <<<\n")
        }
      } catch (e: Exception) {
        append("- Error retrieving wish selection details: ${e.message}\n")
      }
    }
  }

  /** Updates the lastDiscussedTimestamp for a wish when it's selected in the core loop */
  private suspend fun updateWishTimestamp(slot: Int) {
    try {
      // Get the manifestation for this slot
      val manifestation = repository.getManifestationBySlot(slot)

      // Update the timestamp if the manifestation exists
      manifestation?.let {
        repository.updateLastDiscussedTimestamp(it.id)
        logStatus("Updated lastDiscussedTimestamp for wish in slot $slot (ID: ${it.id})")
      }
    } catch (e: Exception) {
      Log.e(TAG, "Error updating wish timestamp: ${e.message}", e)
    }
  }

  /**
   * CoreLoopManager instance for handling Core Loop processing. Following the "frontal cortex"
   * pattern where ConversationAgent orchestrates and CoreLoopManager handles internal processing.
   */
  private val coreLoopManager: CoreLoopManager by lazy {
    CoreLoopManager(agentCortex = agentCortex, logStatus = ::logStatus)
  }

  /** Handles user responses during the Core Loop conversation. */
  private suspend fun handleCoreLoopResponse(response: String) {
    logStatus(
            "🔄 CORE_LOOP: Processing user response: \"${response.take(50)}${if (response.length > 50) "..." else ""}\"",
            StatusColor.Default
    )

    // this looks like a duplicate.  we shouldn't need to add to history because everything's
    // already been added while the agent is listening.
    // Add user response to conversation history
    // addToHistory(Speaker.User, response, currentAction.value)

    // Change dialogue state back to thinking while we process
    logStatus("🔄 CORE_LOOP: Changing dialogue state to Thinking", StatusColor.Default)
    changeDialogueState(DialogueState.Thinking)

    // Get current phase from CoreLoopState
    val currentPhase = agentCortex.coreLoopState.value.currentPhase
    val checkInState = agentCortex.checkInState.value

    logStatus("🔄 CORE_LOOP: Current phase is $currentPhase", StatusColor.Default)

    // Special handling for CHECK_IN phase
    if (currentPhase == ConversationPhase.CHECK_IN) {
      // Check if we're in the TRANSITION stage
      if (checkInState.currentStage == CheckInStage.TRANSITION) {
        logStatus("🔄 CORE_LOOP: Handling transition response", StatusColor.Default)
        //                handleTransitionResponse(response)
      } else {
        logStatus(
                "🔄 CORE_LOOP: Handling CHECK_IN phase response in OPENING stage",
                StatusColor.Default
        )
        // Process CHECK_IN response
        progressCheckIn()
      }
    } else {
      logStatus("🔄 CORE_LOOP: Processing response for $currentPhase phase", StatusColor.Default)
      // For other phases, continue with standard processing
      progressCoreLoop()
    }
  }
}

