package com.example.voxmanifestorapp.ui.agent.commands

import ManifestationRepository
import com.example.voxmanifestorapp.data.MAX_WISH_SLOTS
import com.example.voxmanifestorapp.data.Manifestation
import com.example.voxmanifestorapp.data.StatusColor
import com.example.voxmanifestorapp.ui.agent.ConversationStep
import com.example.voxmanifestorapp.ui.agent.ConversationType
import com.example.voxmanifestorapp.ui.agent.DialogueState
import com.example.voxmanifestorapp.ui.agent.VoxInputType
import com.example.voxmanifestorapp.ui.agent.navigation.NavigationManager
import com.example.voxmanifestorapp.ui.agent.AgentCortex
import com.example.voxmanifestorapp.ui.main.MainScreenState
import com.example.voxmanifestorapp.ui.agent.voice.VoiceProcessor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import com.example.voxmanifestorapp.ui.agent.utilities.WishUtilities
import com.example.voxmanifestorapp.ui.agent.utilities.getOrdinal
import com.example.voxmanifestorapp.ui.agent.utilities.extractNumber
import com.example.voxmanifestorapp.ui.agent.utilities.extractIntNumber
import com.example.voxmanifestorapp.ui.agent.utilities.parseNumberWord

/**
 * Manages command-driven conversation flows for wish collection and selection.
 * These flows operate in administrative/command mode with deterministic state transitions.
 * See README.md in this package for detailed documentation.
 */
class CommandMode(
    private val repository: ManifestationRepository,
    private val wishUtilities: WishUtilities,
    private val agentCortex: AgentCortex,
    private val mainScreenState: MainScreenState,
    private val logger: (String, StatusColor) -> Unit,
    private val voiceProcessor: VoiceProcessor,
    private val navigationManager: NavigationManager
) {
    
    // State machine variables
    private var currentStep: ConversationStep? = null
    private var selectedManifestation: Manifestation? = null
    private var tempWishResponse: String? = null
    private var isSingleCommandMode: Boolean = false

    /**
     * Sets single command mode flag
     */
    fun setSingleCommandMode(value: Boolean) {
        isSingleCommandMode = value
    }

    /**
     * Gets slot availability information for TransitionChain use
     * Returns a descriptive string about available wish slots
     */
    suspend fun getSlotAvailabilityInfo(): String {
        val allManifestations = wishUtilities.getAllManifestations()
        val totalSlots = MAX_WISH_SLOTS
        val filledSlots = allManifestations.size
        val availableSlots = totalSlots - filledSlots

        return when {
            availableSlots == 0 -> "All 5 wish slots are filled"
            availableSlots == 1 -> "1 wish slot available"
            else -> "$availableSlots wish slots available"
        }
    }

    /**
     * Entry point for conversation loop - determines whether to collect or select wishes
     */
    suspend fun enterConversationLoop() {
        // Get all manifestations to check for empty slots
        val allManifestations = wishUtilities.getAllManifestations()
        val emptyWishIndex = wishUtilities.findNextEmptyWishSlot()

        logger("findNextEmptyWishSlot returned $emptyWishIndex", StatusColor.Default)

        if (emptyWishIndex != -1) {
            // If we have an empty wish slot, work with filling that slot
            agentCortex.updateConversationType(ConversationType.CommandMode)
            mainScreenState.setSelectedSlot(emptyWishIndex)
            proceedToNextWishStep(ConversationStep.WishStep.AskForWish)
        } else {
            // If slots are full, we need to run a different process
            agentCortex.updateConversationType(ConversationType.CommandMode)
            proceedToNextSelectionStep(ConversationStep.WishSelectionStep.SelectWish)
        }
    }

    /**
     * Processes input for command mode conversations
     * Routes based on current conversation step to appropriate flow
     */
    suspend fun processInput(text: String, dialogueState: DialogueState) {
        logger("CommandMode received input: $text", StatusColor.Default)
        
        when (dialogueState) {
            // If agent is speaking, ignore user input
            is DialogueState.Speaking -> return

            // If we're expecting input, process according to current step
            is DialogueState.ExpectingInput -> {
                when (currentStep) {
                    // Wish Collection Flow
                    is ConversationStep.WishStep.CaptureWish -> {
                        captureWish(text)
                    }
                    is ConversationStep.WishStep.CaptureValidation -> {
                        resolveYesNo(text) { message ->
                            voiceProcessor.speak(message)
                        }?.let { yesNo ->
                            validateWish(yesNo)
                        }
                    }
                    
                    // Wish Selection Flow
                    is ConversationStep.WishSelectionStep.CaptureWish -> {
                        selectWishNumber(text)
                    }
                    is ConversationStep.WishSelectionStep.CaptureProcess -> {
                        handleProcessSelection(text)
                    }
                    
                    else -> logger("CommandMode: Unexpected step: $currentStep", StatusColor.Default)
                }
            }
            else -> {
                // Unexpected dialogue state
            }
        }
    }

    /**
     * Proceeds to the next step in wish collection flow
     */
    private suspend fun proceedToNextWishStep(step: ConversationStep.WishStep) {
        updateCurrentStep(step)
        when (step) {
            ConversationStep.WishStep.AskForWish -> {
                logger("<wishstep.askforwish>", StatusColor.Default)
                askForWish()
            }
            ConversationStep.WishStep.CaptureWish -> {
                agentCortex.updateDialogueState(DialogueState.ExpectingInput(VoxInputType.FREEFORM))
                logger("Listening for your wish...", StatusColor.Default)
            }
            ConversationStep.WishStep.CheckWish -> {
                logger("<wishstep.checkwish>", StatusColor.Default)
                checkWish()
            }
            ConversationStep.WishStep.CaptureValidation -> {
                agentCortex.updateDialogueState(DialogueState.ExpectingInput(VoxInputType.YES_NO))
            }
            else -> logger("Unexpected wish step: $step", StatusColor.Default)
        }
    }

    /**
     * Proceeds to the next step in wish selection flow
     */
    private suspend fun proceedToNextSelectionStep(step: ConversationStep.WishSelectionStep) {
        updateCurrentStep(step)
        when (step) {
            is ConversationStep.WishSelectionStep.SelectWish -> {
                voiceProcessor.speak("Which wish do you want to edit?")
                proceedToNextSelectionStep(ConversationStep.WishSelectionStep.CaptureWish)
                return
            }
            is ConversationStep.WishSelectionStep.CaptureWish -> {
                logger("Waiting for wish selection", StatusColor.Default)
                agentCortex.updateDialogueState(DialogueState.ExpectingInput(VoxInputType.FREEFORM))
                return
            }
            is ConversationStep.WishSelectionStep.SelectProcess -> {
                voiceProcessor.speak("Would you like to define, change, or delete this wish?")
                proceedToNextSelectionStep(ConversationStep.WishSelectionStep.CaptureProcess)
                return
            }
            is ConversationStep.WishSelectionStep.CaptureProcess -> {
                agentCortex.updateDialogueState(DialogueState.ExpectingInput(VoxInputType.COMMAND_SELECTION))
                return
            }
            else -> logger("Unexpected selection step: $step", StatusColor.Default)
        }
    }

    /**
     * Asks for a wish based on current slot
     */
    private suspend fun askForWish() {
        val wishNumber = mainScreenState.selectedSlot.value + 1
        val prompt = "What is your ${getOrdinal(wishNumber)} wish?"

        voiceProcessor.speak(prompt)
        proceedToNextWishStep(ConversationStep.WishStep.CaptureWish)
    }

    /**
     * Captures wish text from user
     */
    private suspend fun captureWish(text: String) {
        tempWishResponse = text
        proceedToNextWishStep(ConversationStep.WishStep.CheckWish)
    }

    /**
     * Checks captured wish with user
     */
    private suspend fun checkWish() {
        if (tempWishResponse != null) {
            voiceProcessor.speak("I've entered your wish as this... ${tempWishResponse}... is that ok?")
            proceedToNextWishStep(ConversationStep.WishStep.CaptureValidation)
        } else {
            voiceProcessor.speak("I'm sorry, something's gone wrong with retrieving your wish.")
        }
    }

    /**
     * Validates and saves the wish
     */
    private suspend fun validateWish(yesNo: Boolean?) {
        if (yesNo == true) {
            val currentSlot = mainScreenState.selectedSlot.value
            val wishText = tempWishResponse ?: ""
            
            // Use WishUtilities to save the wish
            val success = wishUtilities.saveWishToSlot(wishText, currentSlot)
            
            if (success) {
                voiceProcessor.speak("New manifestation created!")
                logger("Manifestation: \"$wishText\"", StatusColor.Go)
            } else {
                voiceProcessor.speak("I'm sorry, I had trouble saving that wish.")
                logger("Failed to save wish to slot $currentSlot", StatusColor.Stop)
            }

            if (isSingleCommandMode) resetConversation()
            else enterConversationLoop()

        } else {
            voiceProcessor.speak("Let's get that wish from you again...")
            proceedToNextWishStep(ConversationStep.WishStep.CaptureWish)
        }
    }

    /**
     * Selects wish by number from speech
     */
    suspend fun selectWishNumber(text: String) {
        val number = extractNumber(text)

        if (number == null) {
            voiceProcessor.speak("Couldn't hear a wish number. Please say a number between one and five")
            proceedToNextSelectionStep(ConversationStep.WishSelectionStep.CaptureWish)
            return
        }

        // Create array index from human friendly number
        val slot = number - 1

        if (!wishUtilities.isValidWishSlot(slot)) {
            voiceProcessor.speak("Please choose a number between one and $MAX_WISH_SLOTS")
            proceedToNextSelectionStep(ConversationStep.WishSelectionStep.CaptureWish)
            return
        }

        // Error checking done, now run the selection function
        selectWishBySlot(slot, fromAgent = true)
    }

    /**
     * Selects wish by slot index
     */
    suspend fun selectWishBySlot(slot: Int, fromAgent: Boolean = false) {
        val getWish = wishUtilities.getWishBySlot(slot)
        val number = slot + 1

        if (getWish != null) {
            // Set slot index if called from voice command
            if (fromAgent) mainScreenState.setSelectedSlot(slot)

            voiceProcessor.speak("Selected wish number $number - ${getWish.title}")
            selectedManifestation = getWish
            proceedToNextSelectionStep(ConversationStep.WishSelectionStep.SelectProcess)

        } else {
            voiceProcessor.speak("Selected empty slot $number")
            proceedToNextWishStep(ConversationStep.WishStep.AskForWish)
        }
    }

    /**
     * Handles process selection for selected wish
     */
    private suspend fun handleProcessSelection(text: String) {
        when {
            text.contains("delete") || text.contains("remove") -> {
                selectedManifestation?.let { wish ->
                    val success = wishUtilities.deleteWishFromSlot(wish.slot)
                    if (success) {
                        voiceProcessor.speak("Wish deleted")
                        if (isSingleCommandMode) resetConversation()
                        else enterConversationLoop()
                    } else {
                        voiceProcessor.speak("Sorry, I had trouble deleting that wish")
                    }
                } ?: voiceProcessor.speak("Sorry, I couldn't find that wish to delete")
            }
            text.contains("change") || text.contains("edit") || text.contains("add") -> {
                voiceProcessor.speak("Let's set the wish for this slot.")
                agentCortex.updateConversationType(ConversationType.CommandMode)
                proceedToNextWishStep(ConversationStep.WishStep.AskForWish)
            }
            text.contains("present") || text.contains("desired") || 
            text.contains("concept") || text.contains("define") -> {
                val currentSlot = mainScreenState.selectedSlot.value
                voiceProcessor.speak("Elaborating wish ${currentSlot+1}")
                agentCortex.updateConversationType(ConversationType.ConceptBuilding)
                
                // Open concept screen
                selectedManifestation?.let { manifestation ->
                    withContext(Dispatchers.Main) {
                        navigationManager.navigateToConceptScreen(manifestation.id)
                    }
                }
            }
            text.contains("cancel") || text.contains("no") || text.contains("never mind") -> {
                voiceProcessor.speak("Okay, canceling the operation")
                resetConversation()
            }
            else -> {
                voiceProcessor.speak("Please say either 'define', 'change', 'delete', or 'cancel' to stop")
                proceedToNextSelectionStep(ConversationStep.WishSelectionStep.CaptureProcess)
            }
        }
    }

    /**
     * Updates current conversation step
     */
    private fun updateCurrentStep(newStep: ConversationStep?) {
        currentStep = newStep
    }

    /**
     * Resets conversation state
     */
    private fun resetConversation() {
        agentCortex.resetConversationState()
        updateCurrentStep(null)
        agentCortex.updateDialogueState(DialogueState.Idle)
        selectedManifestation = null
        tempWishResponse = null
    }

    // ===== TEXT PROCESSING UTILITIES =====
    // These functions are specific to CommandMode voice command processing

    /**
     * Resolves a potentially ambiguous response into a boolean
     */
    private suspend fun resolveYesNo(text: String, speak: suspend (String) -> Unit): Boolean? {
        logger("Resolving YESNO with $text", StatusColor.Default)
        val lowerText = text.lowercase()
        return when {
            lowerText.contains("yes") || lowerText.contains("yeah") || lowerText.contains("yep") -> true
            lowerText.contains("no") || lowerText.contains("nope") -> false
            else -> {
                speak("I didn't hear a yes or a no. Please say yes or no!")
                return null
            }
        }
    }
}